import {Entity, hasMany, model, property, hasOne } from '@loopback/repository';
import {Quest, QuestRelations, QuestWithRelations} from './quest.model';
import {Image} from './image.model';
import {Segment} from './segment.model';
import {CampaignSegment} from './campaign-segment.model';

@model({
	settings: {
		strict: true
	}
})
export class Campaign extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'number',
		required: true,
	})
	orgId: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
		required: true,
	})
	type: string;

	@property({
		type: 'number',
		generated: true,
	})
	priority?: number;

	@property({
		type: 'date',
		required: true,
	})
	startDate?: string;

	@property({
		type: 'date',
		required: true,
	})
	endDate?: string;

	@property({
		type: 'string',
		required: false,
	})
	description?: string;

	@property({
		type: 'string',
		required: false,
	})
	category?: string;

	@property({
		type: 'boolean',
		required: false,
	})
	hiddenUntilComplete?: boolean;

	@hasMany(() => Quest)
	quests: Quest[];

	@hasOne(() => Image)
	image?: Image;

	@hasMany(() => Segment, {through: {model: () => CampaignSegment}})
	segments?: Segment[];

	status: 'Draft' | 'Running' | 'Completed' = 'Draft';

	constructor(data?: Partial<Campaign>) {
		super(data);

		Object.defineProperty(this, 'status', {
			get() {
				const now = Date.now();
				const start = new Date(this.startDate ? this.startDate : '1/1/1970').getTime();
				const end = new Date(this.endDate ? this.endDate : now + 500 * 365 * 24 * 60 * 60 * 1000).getTime();

				if (now > end) {
					return 'Completed';
				} else if (now < start) {
					return 'Draft';
				} else {
					return 'Running'
				}
			}
		});
	}
}

export interface CampaignRelations {
	// describe navigational properties here
	quests: QuestWithRelations[];
}

export type CampaignWithRelations = Campaign & CampaignRelations;
