import {Entity, model, property, hasMany} from '@loopback/repository';
import {Widget} from './widget.model';

@model({settings: {strict: true}})
export class Dashboard extends Entity {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  organizationId?: number;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  ownerId?: string;

  @property({
    type: 'number',
  })
  shared?: number;

  @hasMany(() => Widget)
  widgets: Widget[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Dashboard>) {
    super(data);
  }
}

export interface DashboardRelations {
  // describe navigational properties here
}

export type DashboardWithRelations = Dashboard & DashboardRelations;
