import {Entity, belongsTo, model, property} from '@loopback/repository';
import {Organization} from './organization.model';
import {Plan, PlanWithRelations} from './plan.model';

@model({settings: {strict: true}})
export class OrganizationPlan extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @belongsTo(() => Organization, {name: 'organization'})
  orgId: number;

  @belongsTo(() => Plan)
  planId: number;

  @property({
    type: 'number',
  })
  priceOverride?: number;

  @property({
    type: 'date',
  })
  lastPaymentDate?: string;

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  subscriptionId?: string;

  @property({
    type: 'string',
  })
  confirmationUrl?: string;

  @property({
    type: 'date',
  })
  scheduledStart?: string;

  @property({
    type: 'date',
  })
  scheduledEnd?: string;

  constructor(data?: Partial<OrganizationPlan>) {
    super(data);
  }

  //   @property({
  //     type: 'date',
  //   })
  //   freeTrialStartDate?: string;

  //   @property({
  //     type: 'date',
  //   })
  //   freeTrialEndDate?: string;

  //   @property({
  //     type: 'date',
  //   })
  //   startDate?: string;

  //   @property({
  //     type: 'date',
  //   })
  //   endDate?: string;
}

export interface OrganizationPlanRelations {
  // describe navigational properties here
  plan: PlanWithRelations;
  organization: Organization;
}

export type OrganizationPlanWithRelations = OrganizationPlan & OrganizationPlanRelations;
