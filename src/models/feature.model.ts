import {Entity, model, property, hasMany} from '@loopback/repository';
import {PlanFeature} from './plan-feature.model';

@model({settings: {strict: true}})
export class Feature extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @hasMany(() => PlanFeature)
  planFeatures: PlanFeature[];

  constructor(data?: Partial<Feature>) {
    super(data);
  }
}

export interface FeatureRelations {
  // describe navigational properties here
}

export type FeatureWithRelations = Feature & FeatureRelations;
