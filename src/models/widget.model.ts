import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: true}})
export class Widget extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  type?: string;

  @property({
    type: 'string',
  })
  data?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  label?: string;

  @property({
    type: 'string',
    required: true,
  })
  projectId: string;

  @property({
    type: 'string',
  })
  size?: string;

  @property({
    type: 'number',
  })
  dashboardId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Widget>) {
    super(data);
  }
}

export interface WidgetRelations {
  // describe navigational properties here
}

export type WidgetWithRelations = Widget & WidgetRelations;
