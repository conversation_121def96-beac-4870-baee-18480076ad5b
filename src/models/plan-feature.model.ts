import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Plan} from './plan.model';
import {Feature} from './feature.model';

@model({settings: {strict: true}})
export class PlanFeature extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'boolean',
    default: true,
  })
  enabled?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  unlimited?: boolean;

  @property({
    type: 'number',
  })
  limit?: number;

  @property({
    type: 'boolean',
    default: true,
  })
  showInUI?: boolean;

  @belongsTo(() => Plan)
  planId: number;

  @belongsTo(() => Feature)
  featureId: string;

  constructor(data?: Partial<PlanFeature>) {
    super(data);
  }
}

export interface PlanFeatureRelations {
  // describe navigational properties here
}

export type PlanFeatureWithRelations = PlanFeature & PlanFeatureRelations;
