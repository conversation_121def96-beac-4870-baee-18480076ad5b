import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: true}})
export class TranslationString extends Entity {
    @property({
        type: 'string',
        generated: false,
        id: true,
    })
    id?: string;

    @property({
        type: 'string',
        required: true,
    })
    key: string;

    @property({
        type: 'string',
        required: true,
    })
    value: string;

    @property({
        type: 'string',
        required: true,
    })
    language: string;

    @property({
        type: 'number',
        required: false,
    })
    orgId?: number;

    constructor(data?: Partial<TranslationString>) {
        super(data);
    }
}


export interface TranslationStringRelations {
	// describe navigational properties here
  }

  export type TranslationStringWithRelations = TranslationString & TranslationStringRelations;
