import {Entity, model, property, hasOne, belongsTo} from '@loopback/repository';
import {Content} from './content.model';
import {Quest} from './quest.model';

@model({settings: {strict: true}})
export class Goal extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	type: string;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'string',
	})
	description?: string;

	@property({
		type: 'object',
		required: true,
	})
	requiredData: object;

	@property({
		type: 'string',
		required: false,
	})
	externalId?: string;

	@hasOne(() => Content)
	content?: Content;

	@belongsTo(() => Quest)
	questId: number;

	constructor(data?: Partial<Goal>) {
		super(data);
	}
}

export interface GoalRelations {
	// describe navigational properties here
}

export type GoalWithRelations = Goal & GoalRelations;
