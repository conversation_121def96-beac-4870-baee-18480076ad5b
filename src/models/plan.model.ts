import {Entity, model, property, hasMany} from '@loopback/repository';
import {PlanRevenuePricing} from './plan-revenue-pricing.model';

@model({settings: {strict: true}})
export class Plan extends Entity {
  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    required: true,
  })
  price: number;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  selectortext?: string;

  @property({
    type: 'string',
  })
  purchasetext?: string;

  @property({
    type: 'string',
  })
  action?: string;

  @property({
    type: 'string',
  })
  image?: string;

  @property({
    type: 'string',
  })
  billingFrequency?: string;

  @property({
    type: 'number',
    id: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  sortOrder?: number;

  @property({
    type: 'boolean',
  })
  active?: boolean;

  @property({
    type: 'boolean',
  })
  hasRevenueBasedPricing?: boolean;

  @hasMany(() => PlanRevenuePricing)
  planRevenuePricings: PlanRevenuePricing[];

  constructor(data?: Partial<Plan>) {
    super(data);
  }
}

export interface PlanRelations {
  // describe navigational properties here
  planRevenuePricings?: PlanRevenuePricing[];
}

export type PlanWithRelations = Plan & PlanRelations;
