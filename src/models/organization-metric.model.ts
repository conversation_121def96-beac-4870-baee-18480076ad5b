import {Entity, belongsTo, model, property} from '@loopback/repository';
import {Organization} from './organization.model';

@model({settings: {strict: true}})
export class OrganizationMetric extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@belongsTo(() => Organization)
	orgId: number;

	@property({
		type: 'number',
		required: true,
	})
	metricId: number;

	@property({
		type: 'string',
	})
	variableOverride?: string;

	@property({
		type: 'string',
	})
	queryOverride?: string;

	@property({
		type: 'date',
	})
	lastRunDate?: string;

	@property({
		type: 'date',
	})
	errorDate?: string;

	@property({
		type: 'string',
	})
	error?: string;

	@property({
		type: 'string',
	})
	runFrequency?: string;

	@property({
		type: 'string',
		required: false,
	})
	fieldMappings?: string;

	constructor(data?: Partial<OrganizationMetric>) {
		super(data);
	}
}

export interface OrganizationMetricRelations {
	// describe navigational properties here
}

export type OrganizationMetricWithRelations = OrganizationMetric & OrganizationMetricRelations;
