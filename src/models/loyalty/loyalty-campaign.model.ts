import {Entity, model, property, belongsTo, hasMany, hasOne} from '@loopback/repository';
import {LoyaltyProgram} from './loyalty-program.model';
import {LoyaltyEarn} from './loyalty-earn.model';
import {LoyaltyRedemptionShopItem} from './loyalty-redemption-shop-item.model';
import {VipTier} from '../vip-tier.model';
import {LoyaltyStaticEffect} from '../loyalty-static-effect.model';

@model({
	settings: {
		strict: true
	}
})
export class LoyaltyCampaign extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'boolean',
		required: true,
	})
	evergreen: boolean;

	@property({
		type: 'boolean',
		required: true,
	})
	active: boolean;

	@property({
		type: 'date',
	})
	startdate?: string;

	@property({
		type: 'date',
	})
	enddate?: string;

	@property({
		type: 'string',
	})
	loyaltySegment?: string;

	@property({
		type: 'string',
	})
	loyaltySegmentType?: string;

	@property({
		type: 'string',
	})
	summaryHTML?: string;

	@belongsTo(() => LoyaltyProgram)
	loyaltyProgramId: number;

	@hasMany(() => LoyaltyEarn)
	loyaltyEarns: LoyaltyEarn[];

	@hasMany(() => LoyaltyRedemptionShopItem)
	loyaltyRedemptionShopItems: LoyaltyRedemptionShopItem[];

	live: boolean = false;
	liveStatus: string = '';
	@hasOne(() => VipTier)
	vipTier?: VipTier;

	@hasMany(() => LoyaltyStaticEffect)
	staticEffects: LoyaltyStaticEffect[];

	constructor(data?: Partial<LoyaltyCampaign>) {
		super(data);

		Object.defineProperty(this, 'live', {
			get() {
				if (this.active && this.evergreen) return true;

				const now = new Date();
				const start: Date = new Date(this.startdate || '1970-01-01');
				const end: Date = new Date(this.enddate || '9999-12-31');
				return this.active && now >= start && now <= end;
			}
		});

		Object.defineProperty(this, 'liveStatus', {
			get() {
				if (!this.active) return 'Not Active';

				const now = new Date();
				const start = new Date(this.startdate || '1970-01-01');
				const end = new Date(this.enddate || '9999-12-31');

				if (this.evergreen) {
					return 'Live'; // Evergreen campaigns are always live if active
				} else if (now < start) {
					return 'Pending'; // Campaign hasn't started yet
				} else if (now > end) {
					return 'Completed'; // Campaign has ended
				} else {
					return 'Live'; // Campaign is currently running
				}
			}
		});
	}
}

export interface LoyaltyCampaignRelations {
	// describe navigational properties here
	loyaltyProgram: LoyaltyProgram;
}

export type LoyaltyCampaignWithRelations = LoyaltyCampaign & LoyaltyCampaignRelations;
