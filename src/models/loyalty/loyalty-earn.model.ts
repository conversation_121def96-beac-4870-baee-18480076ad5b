import {Entity, model, property, hasMany, belongsTo} from '@loopback/repository';
import {EarnEffect} from './earn-effect.model';
import {LoyaltyCampaign} from './loyalty-campaign.model';
import {EarnCondition} from './earn-condition.model';

@model({
	settings: {
		strict: true
	}
})
export class <PERSON>yaltyEarn extends Entity {
	@property({
		type: 'number',
		id: true,
		generated: true,
	})
	id?: number;

	@property({
		type: 'string',
		required: true,
	})
	name: string;

	@property({
		type: 'boolean',
		default: false
	})
	active: boolean;

	@property({
		type: 'boolean',
		default: false
	})
	archived: boolean;

	@property({
		type: 'boolean',
		default: false
	})
	hiddenFromLoyaltyUi: boolean;

	@property({
		type: 'boolean',
		default: false
	})
	hiddenFromAdminUi: boolean;

	@property({
		type: 'string',
	})
	description: string;

	@property({
		type: 'string',
	})
	imageURL: string;

	@property({
		type: 'number',
		default: 999
	})
	priority: number;

	@property({
		type: 'string',
	})
	quest_def_id?: string;

	@property({
		type: 'boolean',
		default: false
	})
	recommendedByAI: boolean;

	@property({
		type: 'string',
		jsonSchema: {
			enum: ['NONE', 'RECOMMENDED', 'IGNORED', 'APPROVED_RECOMMENDATION']
		},
		default: 'NONE'
	})
	recommendationState?: string;

	@hasMany(() => EarnEffect)
	earnEffects: EarnEffect[];

	@belongsTo(() => LoyaltyCampaign)
	loyaltyCampaignId: number;

	@hasMany(() => EarnCondition)
	earnConditions: EarnCondition[];

    isRecommendation: boolean = false;
    ignoreRecommendation: boolean = false;

	constructor(data?: Partial<LoyaltyEarn>) {
		super(data);

		Object.defineProperty(this, 'isRecommendation', {
			get() {
				return this.recommendedByAI && this.recommendationState === 'RECOMMENDED';
			}
		});

		Object.defineProperty(this, 'ignoreRecommendation', {
			get() {
				return this.recommendedByAI && this.recommendationState === 'IGNORED';
			}
		});
	}
}

export interface LoyaltyEarnRelations {
	// describe navigational properties here
}

export type LoyaltyEarnWithRelations = LoyaltyEarn & LoyaltyEarnRelations;
