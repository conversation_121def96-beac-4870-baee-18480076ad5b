import {
  authenticate,
  TokenService,
  UserService
} from '@loopback/authentication';
import {TokenServiceBindings} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {model, property, repository} from '@loopback/repository';
import {
  api,
  del,
  get, HttpErrors,
  param,
  patch,
  post,
  put,
  Request,
  requestBody,
  RestBindings
} from '@loopback/rest';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import isemail from 'isemail';
import _ from 'lodash';
import {PasswordHasherBindings, UserServiceBindings} from '../keys';
import {IDENTITY_TYPES, Organization, ResetPasswordInit, User, UserIdentity} from '../models';

import {authorize} from '@loopback/authorization';
import {SendEmailResponse} from 'aws-sdk/clients/ses';
import {
  CredentialsRequestBody,
  PasswordResetRequestBody,
  UserProfileSchema
} from '../models';
import {KeyAndPassword} from '../models/user-management';
import {Credentials, UserIdentityRepository, UserRepository, OrganizationRepository} from '../repositories';
import {
  basicAuthorization,
  PasswordHasher,
  UserManagementService,
  validateCredentials,
  validateKeyPassword
} from '../services';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, GuardSkipStrategy, guardStrategy, skipGuardCheck, injectUserOrgId, restrictReadsWithGuard} from '../interceptors/crud-guard.interceptor';
import {UserCredentialsRepository} from '../repositories/user-credentials.repository';
const sign = require('jsonwebtoken').sign;

@model()
export class NewUserRequest {
  @property({
    type: 'string',
    required: true,
  })
  email: string;

  @property({
    type: 'string',
    required: true,
  })
  password: string;
}

const OPERATION_SECURITY_SPEC = [{jwt: []}];

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<User>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: UserRepository
}))
export class UserManagementController {
  constructor(
    @repository(UserRepository)
    public userRepository: UserRepository,
	@repository(UserCredentialsRepository)
	private userCredentialsRepository: UserCredentialsRepository,
    @inject(PasswordHasherBindings.PASSWORD_HASHER)
    public passwordHasher: PasswordHasher,
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(UserServiceBindings.USER_SERVICE)
    public userService: UserService<User, Credentials>,
	@repository(UserIdentityRepository)
	protected userIdentityRepository: UserIdentityRepository,
	@repository(OrganizationRepository)
	protected organizationRepository: OrganizationRepository,
    @inject(UserServiceBindings.USER_SERVICE)
    public userManagementService: UserManagementService,
    @inject(SecurityBindings.USER, {optional: true})
    public user: UserProfile,
  ) { }

  @post('/user', {
    responses: {
      '200': {
        description: 'Create User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @skipGuardCheck()
  async create(
    @requestBody(NewUserRequest) newUserRequest: NewUserRequest,
    //Simplified this to just take email and password, and then build the user off of that - Adam
    //Mainly because I couldn't figure out how to get it to work otherwise
  ): Promise<User> {
    // All new users have the "customer" role by default
    //newUserRequest.roles = ['customer'];
    // ensure a valid email value and password value
    validateCredentials(_.pick(newUserRequest, ['email', 'password']));

    try {
      //newUserRequest.resetKey = '';
      return await this.userManagementService.createUser(newUserRequest);
    } catch (error) {
      throw error;
    }
  }

  @put('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer'],
    voters: [basicAuthorization],
  })
  async set(
	@modelIdForGuard(User)
    @param.path.string('userId')
	userId: typeof User.prototype.id,

    @requestBody({description: 'update user'})
	@modelForGuard(User)
	user: User,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<void> {
    try {
      // Only admin can assign roles
      if (!currentUserProfile.roles.includes('admin')) {
        delete user.roles;
      }
      return await this.userRepository.updateById(userId, user);
    } catch (e) {
      return e;
    }
  }

  @patch('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'raleon-support'],
    voters: [basicAuthorization],
  })
  async updateUser(
	@modelIdForGuard(User)
    @param.path.string('userId')
	userId: typeof User.prototype.id,

    @requestBody({description: 'update user'})
	@modelForGuard(User)
	user: Partial<User>,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<void> {
    try {
      // Only admin can assign roles
      if (!currentUserProfile.roles.includes('admin')) {
        delete user.roles;
      }
      return await this.userRepository.updateById(userId, user);
    } catch (e) {
      return e;
    }
  }

  @get('/users/{userId}', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @restrictReadsWithGuard({ plural: false })
  async findById(
	@param.path.string('userId') userId: typeof User.prototype.id
): Promise<User> {
	return this.userRepository.findById(userId);
  }

  @post('/users/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @skipGuardCheck()
  async login(
    @requestBody(CredentialsRequestBody) credentials: Credentials,
  ): Promise<{token: string, shopifyRaleonSnippetId: string}> {
    // ensure the user exists, and the password is correct
    const user = await this.userManagementService.verifyCredentials(credentials);

	if (user.organizationId) {
		await this.organizationRepository.updateById(user.organizationId, {uninstalledDate: null as any});
	}

    // convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.userManagementService.convertToUserProfile(user);

    // create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);
    const shopifyRaleonSnippetId = process.env.SHOPIFY_RALEON_SNIPPET_ID!;
    return {token, shopifyRaleonSnippetId};
  }

  @post('/users/google-login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {type: 'string'},
              },
            },
          },
        },
      },
    },
  })
  @skipGuardCheck()
  async googleLogin(
    @requestBody() body: {email?: string, name?: string, access_token?: string, idToken?: string},
  ): Promise<{token: string}> {
    let email: string;

    // Handle both old idToken format and new OAuth2 format
    if (body.idToken) {
      // Old format - verify the ID token
      const payload = await this.userManagementService.verifyGoogleToken(body.idToken);
      if (!payload.email) {
        throw new HttpErrors.Unauthorized('Invalid Google token');
      }
      email = payload.email;
    } else if (body.email && body.access_token) {
      // New format - verify the access token by calling Google's API
      try {
        const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
          headers: {
            'Authorization': `Bearer ${body.access_token}`
          }
        });
        const userInfo = await response.json();

        if (!response.ok || !userInfo.email) {
          throw new HttpErrors.Unauthorized('Invalid Google access token');
        }

        // Verify the email matches what was sent
        if (userInfo.email !== body.email) {
          throw new HttpErrors.Unauthorized('Email mismatch in Google response');
        }

        email = userInfo.email;
      } catch (error) {
        throw new HttpErrors.Unauthorized('Failed to verify Google access token');
      }
    } else {
      throw new HttpErrors.BadRequest('Either idToken or (email + access_token) required');
    }

    let user = await this.userRepository.findOne({where: {email}});
    if (!user) {
      throw new HttpErrors.NotFound('User not found');
    }
    const userProfile = this.userManagementService.convertToUserProfile(user);
    const token = await this.jwtService.generateToken(userProfile);
    return {token};
  }

  @post('/users/check-email', {
    responses: {
      '200': {
        description: 'Email check result',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                exists: {type: 'boolean'},
                message: {type: 'string'},
              },
            },
          },
        },
      },
    },
  })
  @skipGuardCheck()
  async checkEmailExists(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['email'],
            properties: {
              email: {type: 'string'},
            },
          },
        },
      },
    })
    body: {email: string},
  ): Promise<{exists: boolean; message: string}> {
    const {email} = body;

    // Check for existing primary user with this email
    const existingUser = await this.userRepository.findOne({
      where: {
        or: [
          { email, isSecondaryAccount: false },
          { email, isSecondaryAccount: null as any }
        ]
      },
    });

    if (existingUser) {
      return {
        exists: true,
        message: 'An account with this email already exists. Please sign in instead.',
      };
    }

    return {
      exists: false,
      message: 'Email is available for signup.',
    };
  }

  @get('/user/orgs', {
    responses: {
      '200': {
        description: 'Orgs',
        content: {
          'application/json': {
            schema: {
              type: 'array'
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'raleon-support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async getOrgs(
	@inject(RestBindings.Http.REQUEST) request: Request,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<Array<any>> {
	const user = await this.userRepository.findById(currentUserProfile.id);

	// Get organizations where user has direct access
	const otherUsers = await this.userRepository.find({
		where: {
			email: user.email,
		},
		include: ['organization']
	});

	const userOrgs = otherUsers.map(x => ({
		id: x.organizationId,
		name: x.organization?.name,
		parentId: x.organization?.parentOrgId,
		orgType: x.organization?.orgType
	}));

	// For agency organizations, also include all child organizations
	const agencyOrgs = userOrgs.filter(org => org.orgType === 'agency');
	const childOrgs = [];

	for (const agencyOrg of agencyOrgs) {
		// Find all organizations that have this agency as their parent
		const children = await this.organizationRepository.find({
			where: {
				parentOrgId: agencyOrg.id
			}
		});

		// Add child organizations to the list
		for (const child of children) {
			childOrgs.push({
				id: child.id,
				name: child.name,
				parentId: child.parentOrgId,
				orgType: child.orgType
			});
		}
	}

	// Combine user orgs and child orgs, removing duplicates
	const allOrgs = [...userOrgs];
	for (const childOrg of childOrgs) {
		// Only add if not already in the list (user might have direct access to child org too)
		if (!allOrgs.some(org => org.id === childOrg.id)) {
			allOrgs.push(childOrg as any);
		}
	}

    return allOrgs;
  }



  @del('/user', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'raleon-support'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async deleteUser(
	@inject(RestBindings.Http.REQUEST) request: Request,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<{token: string}> {
	const user = await this.userRepository.findById(currentUserProfile.id);
	if (!user) {
		throw HttpErrors.NotFound('User not found');
	}

	const otherUser = await this.userRepository.findOne({
		where: {
			email: user.email,
			id: { neq: user.id },
			organizationId: { gt: 0 }
		}
	});

	if (!otherUser) {
		throw HttpErrors.BadRequest('Not allowed to leave only project');
	}

	if (!user.isSecondaryAccount) {
		const credentials = await this.userCredentialsRepository.findOne({
			where: {
				userId: user.id
			}
		});
		if (!credentials) {
			throw HttpErrors.NotFound('User credentials not found');
		}

		credentials!.userId = otherUser?.id!;
		otherUser!.isSecondaryAccount = false;

		await this.userCredentialsRepository.update(credentials);
		await this.userRepository.update(otherUser);
	}

	await this.userRepository.deleteById(currentUserProfile.id);


    const userProfile = this.userManagementService.convertToUserProfile(otherUser);
    const token = await this.jwtService.generateToken(userProfile);

    return {token};
  }

  @post('/users/login/{orgId}', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'raleon-support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async loginToOrg(
	@inject(RestBindings.Http.REQUEST) request: Request,
	@param.path.number('orgId') orgId: typeof Organization.prototype.id,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<{token: string}> {
	const user = await this.userRepository.findById(currentUserProfile.id);
	const otherUser = await this.userRepository.findOne({
		where: {
			email: user.email,
			organizationId: orgId
		}
	});
	if (!otherUser) {
		throw HttpErrors.NotFound('User not found');
	}

    const userProfile = this.userManagementService.convertToUserProfile(otherUser);
    const token = await this.jwtService.generateToken(userProfile);

    return {token};
  }

  @post('/users/login-agency/{orgId}', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'customer-admin', 'raleon-support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async loginToOrgAgency(
	@inject(RestBindings.Http.REQUEST) request: Request,
	@param.path.number('orgId') orgId: typeof Organization.prototype.id,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<{token: string}> {
	const user = await this.userRepository.findById(currentUserProfile.id);

	// Check if user is an agency admin (has admin role but not raleon-admin or raleon-support)
	const isAgencyAdmin = user.roles?.includes('admin') &&
		!user.roles?.includes('raleon-admin') &&
		!user.roles?.includes('raleon-support');

	if (!isAgencyAdmin) {
		throw HttpErrors.Forbidden('Only agency admins can use this endpoint');
	}

	// Check if user's current organization is an agency
	const currentOrg = await this.organizationRepository.findById(user.organizationId);
	if (currentOrg.orgType !== 'agency') {
		throw HttpErrors.Forbidden('User must belong to an agency organization');
	}

	// Check if target organization is a child of the agency
	const targetOrg = await this.organizationRepository.findById(orgId);
	if (targetOrg.parentOrgId !== user.organizationId) {
		throw HttpErrors.Forbidden('Target organization must be a child of the agency');
	}

	// Try to find existing user in target organization
	let otherUser = await this.userRepository.findOne({
		where: {
			email: user.email,
			organizationId: orgId
		}
	});

	// If user doesn't exist in target org, create them just-in-time
	if (!otherUser) {
		const newUserRequest = {
			email: user.email,
			firstName: user.firstName || user.email,
			lastName: user.lastName,
			organizationId: orgId,
			isSecondaryAccount: true,
			roles: ['admin'], // Give admin role in the brand organization
			avatarColors: user.avatarColors
		};

		otherUser = await this.userRepository.create(newUserRequest);
		console.log(`Created just-in-time user ${user.email} in organization ${orgId} with ID ${otherUser.id}`);
	}

	// Ensure otherUser is not null at this point
	if (!otherUser) {
		throw HttpErrors.InternalServerError('Failed to create or find user in target organization');
	}

    const userProfile = this.userManagementService.convertToUserProfile(otherUser);
    const token = await this.jwtService.generateToken(userProfile);

    return {token};
  }

  @post('/admin/login/{orgId}', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-admin'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  async loginToOrgAdmin(
        @inject(RestBindings.Http.REQUEST) request: Request,
        @param.path.number('orgId') orgId: typeof Organization.prototype.id,

    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<{token: string}> {
        const user = await this.userRepository.findById(currentUserProfile.id);

        const currentOrg = await this.organizationRepository.findById(
          user.organizationId,
        );

        if (currentOrg.isDemoOrg) {
          throw new HttpErrors.Forbidden(
            'Demo users are not allowed to switch organizations',
          );
        }

        user.organizationId = orgId!;
        await this.userRepository.updateById(user.id, user);

    const userProfile = this.userManagementService.convertToUserProfile(user);
    const token = await this.jwtService.generateToken(userProfile);

    return {token};
  }

  @post('/users/login/token', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @skipGuardCheck()
  async loginToken(
	@inject(RestBindings.Http.REQUEST) request: Request,
  ): Promise<{token: string, shopifyRaleonSnippetId: string}> {
	console.log('started loginToken');
	const sessionToken = request.headers?.authorization?.split(' ')?.[1];
	if (!sessionToken) {
		throw new HttpErrors.Unauthorized('Invalid Session Token');
	}

	const userIdentity = await this.userIdentityRepository.findOne({
		where: {
			identityType: IDENTITY_TYPES.SELF_SERVICE,
			sessionToken
		}
	})
	const accessToken = userIdentity?.identityValue;

    if (!accessToken) {
		throw new HttpErrors.UnprocessableEntity('Invalid request');
	}

    const user = await this.userManagementService.verifyToken(accessToken);

	if (user.organizationId) {
		await this.organizationRepository.updateById(user.organizationId, {uninstalledDate: null as any});
	}

    const userProfile = this.userManagementService.convertToUserProfile(user);

    const token = await this.jwtService.generateToken(userProfile);

	console.log('shopifyRaleonSnippetId:: ', process.env.SHOPIFY_RALEON_SNIPPET_ID);
	const shopifyRaleonSnippetId = process.env.SHOPIFY_RALEON_SNIPPET_ID!;
	console.log('end loginToken');
    return {token, shopifyRaleonSnippetId};
  }

  @get('/users/doc-login', {
    responses: {
      '200': {
        description: 'Will login and redirect to the document page',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                docs_url: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  @skipGuardCheck()
  @authenticate('jwt')
  @authorize({
    allowedRoles: ['raleon-support', 'admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  async documentLogin(
	@inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<{docs_url: string}> {
	const userId = parseInt(currentUserProfile[securityId]);
    const userInfo = await this.userRepository.findById(userId);
	let firstName = userInfo.firstName;
	let response = {
		docs_url: 'https://docs.raleon.io',
	}
	let docUser = {
		name: firstName,
		email: userInfo.email,
		apiKey : {
			user: userInfo.email,
			password: 'doc_password_123'
		},
		allowedProjects: ['raleon'],
		version: 1
	}
	const auth_token = sign(docUser, 'Mb3UPeYmGWhZJlZp69XH');
	response.docs_url = response.docs_url + '?auth_token=' + auth_token;

    return response;
  }

  @put('/users/forgot-password', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The updated user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @skipGuardCheck()
  @authenticate('jwt')
  async forgotPassword(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody(PasswordResetRequestBody) credentials: Credentials,
  ): Promise<{token: string}> {
    const {email, password} = credentials;
    const {id} = currentUserProfile;

    const user = await this.userRepository.findById(id);

    if (!user) {
      throw new HttpErrors.NotFound('User account not found');
    }

    if (email !== user?.email) {
      throw new HttpErrors.Forbidden('Invalid email address');
    }

    validateCredentials(_.pick(credentials, ['email', 'password']));

    const passwordHash = await this.passwordHasher.hashPassword(password);

    await this.userRepository
      .userCredentials(user.id)
      .patch({password: passwordHash});

    const userProfile = this.userService.convertToUserProfile(user);

    const token = await this.jwtService.generateToken(userProfile);

    return {token};
  }

  @post('/users/reset-password/init', {
    responses: {
      '200': {
        description: 'Confirmation that reset password email has been sent',
      },
    },
  })
  @skipGuardCheck()
  async resetPasswordInit(
    @requestBody() resetPasswordInit: ResetPasswordInit,
  ): Promise<string> {
    if (!isemail.validate(resetPasswordInit.email)) {
      throw new HttpErrors.UnprocessableEntity('Invalid email address');
    }

    const sentMessageInfo: SendEmailResponse =
      await this.userManagementService.requestPasswordReset(
        resetPasswordInit.email,
      );


    if (sentMessageInfo.MessageId) {
      return 'Successfully sent reset password link';
    }

    throw new HttpErrors.InternalServerError(
      'Error sending reset password email',
    );
  }

  @put('/users/reset-password/finish', {
    responses: {
      '200': {
        description: 'A successful password reset response',
      },
    },
  })
  @skipGuardCheck()
  async resetPasswordFinish(
    @requestBody() keyAndPassword: KeyAndPassword,
  ): Promise<any> {
    validateKeyPassword(keyAndPassword);

    const foundUser = await this.userRepository.findOne({
      where: {resetKey: keyAndPassword.resetKey},
    });

    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No associated account for the provided reset key',
      );
    }

    const user = await this.userManagementService.validateResetKeyLifeSpan(
      foundUser,
    );

    const passwordHash = await this.passwordHasher.hashPassword(
      keyAndPassword.password,
    );

    try {
      await this.userRepository
        .userCredentials(user.id)
        .patch({password: passwordHash});

      await this.userRepository.updateById(user.id, user);
    } catch (e) {
      return e;
    }

    return {
      statusCode: 200,
      body: 'Password reset successful'
    };
  }

  @get('/users/who-am-i', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @skipGuardCheck()
  @authenticate('jwt')
  async printCurrentUser(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<User> {
    // explore a way to generate OpenAPI schema
    // for symbol property

    const userId = parseInt(currentUserProfile[securityId]);
    const user = await this.userRepository.findById(userId);
	let hash;

	try {
		const crypto = require('crypto');

		const secretKey = 'OJCZhA7WUec1Ioue4ydj9pWkMMmh59Oi4IzIaJYR'; // secret key (keep safe!)
		const userIdentifier = user.email; // user's email address

		hash = crypto.createHmac('sha256', secretKey).update(userIdentifier).digest('hex');
	} catch (e) {
		console.error(e);
	}

	return {
		...user,
		intercomUserHash: hash
	} as any;
  }

  @get('/users/is-raleon-support', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @skipGuardCheck()
  @authenticate('jwt')
  async isRaleonSupport(
	@inject(SecurityBindings.USER)
	currentUserProfile: UserProfile,
  ): Promise<boolean> {
	const userId = parseInt(currentUserProfile[securityId]);
	const user = await this.userRepository.findById(userId);
	console.log('roles:: ', JSON.stringify(user.roles));
	return user.roles?.includes('raleon-support') || false;
  }

  @get('/users/is-admin-or-support', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'The current user profile',
        content: {
          'application/json': {
            schema: UserProfileSchema,
          },
        },
      },
    },
  })
  @skipGuardCheck()
  @authenticate('jwt')
  async isAdminOrSupport(
	@inject(SecurityBindings.USER)
	currentUserProfile: UserProfile,
  ): Promise<boolean> {

	const userId = parseInt(currentUserProfile[securityId]);
	const user = await this.userRepository.findById(userId);
	return user.roles?.includes('customer-admin') ||
		user.roles?.includes('admin') ||
		user.roles?.includes('raleon-support') ||
		false;
  }
}
