import {
	lifeCycleObserver,
	LifeCycleObserver,
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	OrganizationPlanRepository,
	PlanFeatureRepository,
	MetricRepository,
	OrganizationMetricRepository
} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';
import {Metric} from '../models';

@lifeCycleObserver('Data')
export class MetricsObserver extends ALifeCycleObserver {
	constructor(
		@repository(OrganizationPlanRepository)
		private organizationPlanRepository: OrganizationPlanRepository,
		@repository(PlanFeatureRepository)
		private planFeatureRepository: PlanFeatureRepository,
		@repository(MetricRepository)
		private metricRepository: MetricRepository,
		@repository(OrganizationMetricRepository)
		private organizationMetricRepository: OrganizationMetricRepository,
	) {
		super();
	}

	async init(): Promise<void> {
		// Add your logic for init
	}

	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		console.log("STARTING METRICS OBSERVER");
		await this.populateMetrics();
	}

	private async populateMetrics(): Promise<void> {
		const metricData: Partial<Metric>[] = [
			{
				name: "repeat_purchase_monthly_cohort",
				query: "WITH first_purchases AS (     SELECT          customer,         MIN(created_at) as first_purchase_date     FROM \"{database}\".filtered_orders     WHERE organization = '{orgId}'     GROUP BY customer ), second_purchases AS (     SELECT          fp.customer,         fp.first_purchase_date,         MIN(o.created_at) as second_purchase_date     FROM first_purchases fp     LEFT JOIN \"{database}\".filtered_orders o ON fp.customer = o.customer         AND o.created_at > fp.first_purchase_date AND o.organization = '{orgId}'     GROUP BY fp.customer, fp.first_purchase_date ), cohorts AS (     SELECT          DATE_TRUNC('month', first_purchase_date) as cohort_month,         COUNT(DISTINCT customer) as cohort_size,         COUNT(DISTINCT CASE WHEN second_purchase_date IS NOT NULL THEN customer END) as converted_customers,         ARRAY[             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 1, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 2, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 3, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 4, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 5, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 6, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 7, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 8, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 9, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 10, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 11, first_purchase_date) THEN customer END),             COUNT(DISTINCT CASE WHEN second_purchase_date <= DATE_ADD('month', 12, first_purchase_date) THEN customer END)         ] as monthly_conversions     FROM second_purchases     WHERE DATE_TRUNC('month', first_purchase_date) >= DATE_ADD('month', -12, DATE_TRUNC('month', CURRENT_DATE))     GROUP BY DATE_TRUNC('month', first_purchase_date) ) SELECT      cohort_month,     cohort_size,      CASE WHEN DATE_ADD('month', 1, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[1] * 100.0 \/ cohort_size, 2) END as conv_1m,     CASE WHEN DATE_ADD('month', 2, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[2] * 100.0 \/ cohort_size, 2) END as conv_2m,     CASE WHEN DATE_ADD('month', 3, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[3] * 100.0 \/ cohort_size, 2) END as conv_3m,     CASE WHEN DATE_ADD('month', 4, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[4] * 100.0 \/ cohort_size, 2) END as conv_4m,     CASE WHEN DATE_ADD('month', 5, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[5] * 100.0 \/ cohort_size, 2) END as conv_5m,     CASE WHEN DATE_ADD('month', 6, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[6] * 100.0 \/ cohort_size, 2) END as conv_6m,     CASE WHEN DATE_ADD('month', 7, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[7] * 100.0 \/ cohort_size, 2) END as conv_7m,     CASE WHEN DATE_ADD('month', 8, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[8] * 100.0 \/ cohort_size, 2) END as conv_8m,     CASE WHEN DATE_ADD('month', 9, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[9] * 100.0 \/ cohort_size, 2) END as conv_9m,     CASE WHEN DATE_ADD('month', 10, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[10] * 100.0 \/ cohort_size, 2) END as conv_10m,     CASE WHEN DATE_ADD('month', 11, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[11] * 100.0 \/ cohort_size, 2) END as conv_11m,     CASE WHEN DATE_ADD('month', 12, cohort_month) <= CURRENT_DATE           THEN ROUND(monthly_conversions[12] * 100.0 \/ cohort_size, 2) END as conv_12m FROM cohorts ORDER BY cohort_month DESC; ",
				dataStructure: "{ \"cohort_month\": {\"type\":\"TIMESTAMP\", \"label\": \"Cohort Month\", \"prefix\": \"\"}, \"cohort_size\": {\"type\":\"INT64\", \"label\": \"Cohort Size\", \"prefix\": \"\"}, \"conv_1m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 1mo\", \"prefix\": \"\"}, \"conv_2m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 2mo\", \"prefix\": \"\"}, \"conv_3m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 3mo\", \"prefix\": \"\"}, \"conv_4m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 4mo\", \"prefix\": \"\"}, \"conv_5m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 5mo\", \"prefix\": \"\"}, \"conv_6m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 6mo\", \"prefix\": \"\"}, \"conv_7m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 7mo\", \"prefix\": \"\"}, \"conv_8m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 8mo\", \"prefix\": \"\"}, \"conv_9m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 9mo\", \"prefix\": \"\"}, \"conv_10m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 10mo\", \"prefix\": \"\"}, \"conv_11m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 11mo\", \"prefix\": \"\"}, \"conv_12m\": {\"type\":\"DOUBLE\", \"label\": \"Conversions 12mo\", \"prefix\": \"\"} }",
				defaultRunFrequency: "month",
				priority: 6901,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks monthly cohorts of first-time customers and calculates the percentage who make a second purchase within 1 to 12 months. Used to measure long-term retention and repeat behavior across customer acquisition cohorts."
			},
			{
				name: "r12_revenue",
				query: "WITH distinct_orders AS ( SELECT DISTINCT customer, id, total_price, created_at, date_trunc('month', created_at) as order_month FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' ), first_purchase_dates AS ( SELECT customer, MIN(created_at) as first_purchase_date FROM distinct_orders GROUP BY customer ), monthly_customer_revenue AS ( SELECT o.order_month, o.customer, SUM(o.total_price) as revenue, CASE WHEN o.created_at = fpd.first_purchase_date THEN 'new' ELSE 'repeat' END as customer_type FROM distinct_orders o JOIN first_purchase_dates fpd ON o.customer = fpd.customer GROUP BY o.order_month, o.customer, CASE WHEN o.created_at = fpd.first_purchase_date THEN 'new' ELSE 'repeat' END ), monthly_metrics AS ( SELECT order_month, SUM( CASE WHEN customer_type = 'new' THEN revenue ELSE 0 END ) as new_customer_revenue, SUM( CASE WHEN customer_type = 'repeat' THEN revenue ELSE 0 END ) as repeat_customer_revenue, COUNT(DISTINCT customer) as active_customers FROM monthly_customer_revenue GROUP BY order_month ) SELECT order_month, AVG(new_customer_revenue) OVER ( ORDER BY order_month ROWS BETWEEN 11 PRECEDING AND CURRENT ROW ) as avg_new_customer_revenue, AVG(repeat_customer_revenue) OVER ( ORDER BY order_month ROWS BETWEEN 11 PRECEDING AND CURRENT ROW ) as avg_repeat_customer_revenue, AVG(active_customers) OVER ( ORDER BY order_month ROWS BETWEEN 11 PRECEDING AND CURRENT ROW ) as avg_active_customers FROM monthly_metrics ORDER BY order_month DESC ",
				dataStructure: "{ \"order_month\": { \"type\": \"datetime\" }, \"avg_new_customer_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\"}, \"avg_repeat_customer_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\"}, \"avg_active_customers\": { \"type\": \"DOUBLE\" }}",
				defaultRunFrequency: "month",
				priority: 10100,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates the rolling 12-month average of revenue from new and repeat customers, as well as average active customers per month. Useful for tracking long-term revenue trends and customer engagement patterns."
			},
			{
				name: "product_sequence",
				type: "product-sequence",
				dataStructure: "{ \"data\": { \"type\": \"UTF8\", \"label\": \"data\", \"prefix\": \"\" } }",
				defaultRunFrequency: "week",
				priority: 6801,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes the sequence of products purchased by customers to identify common purchase patterns and product associations. Used to understand customer buying behavior and inform product recommendations."
			},
			{
				name: "product_category_conversions",
				query: "WITH first_purchases AS ( SELECT customer, CASE WHEN NULLIF(TRIM(product_category), '') IS NULL THEN 'Uncategorized' ELSE COALESCE(TRIM(product_category), 'Uncategorized') END AS product_category, MIN(created_at) AS first_purchase_date FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' GROUP BY customer, CASE WHEN NULLIF(TRIM(product_category), '') IS NULL THEN 'Uncategorized' ELSE COALESCE(TRIM(product_category), 'Uncategorized') END ), second_purchases AS ( SELECT customer, COUNT(DISTINCT id) AS total_purchases FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' GROUP BY customer HAVING COUNT(DISTINCT id) >= 2 ), category_conversion AS ( SELECT fp.product_category, COUNT(DISTINCT fp.customer) AS first_purchase_customers, COUNT( DISTINCT CASE WHEN sp.customer IS NOT NULL THEN fp.customer END ) AS converted_customers, ROUND( 100.0 * COUNT( DISTINCT CASE WHEN sp.customer IS NOT NULL THEN fp.customer END ) \/ COUNT(DISTINCT fp.customer), 2 ) AS conversion_rate FROM first_purchases fp LEFT JOIN second_purchases sp ON fp.customer = sp.customer GROUP BY fp.product_category ) SELECT product_category, first_purchase_customers, converted_customers, conversion_rate FROM category_conversion ORDER BY conversion_rate DESC LIMIT 10",
				dataStructure: "{\"product_category\": { \"type\": \"UTF8\", \"label\": \"Product Category\", \"prefix\": \"\" },\"first_purchase_customers\": { \"type\": \"INT64\", \"label\": \"First Purchase Customers\"},\"converted_customers\": { \"type\": \"INT64\", \"label\": \"Converted Customers\"},\"conversion_rate\": { \"type\": \"DOUBLE\", \"label\": \"Conversion Rate\", \"suffix\": \"%\" }}",
				defaultRunFrequency: "week",
				priority: 10000,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Measures the conversion rate and customer counts for different product categories, helping identify which categories are most effective at driving repeat purchases."
			},
			{
				name: "top_products_new_customers",
				query: "WITH latest_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' GROUP BY customer ), top_5_products AS ( SELECT o.item_name, o.item_id, SUM(o.item_quantity) AS total_quantity_sold FROM \"{database}\".filtered_orders o JOIN latest_segments s ON o.customer = s.customer WHERE s.segment = 'New Users' AND o.item_price > 0 AND o.organization = '{orgId}' GROUP BY o.item_name, o.item_id ORDER BY total_quantity_sold DESC LIMIT 5 ) SELECT o.item_name, o.item_id, COALESCE(NULLIF(s.segment, ''), 'Unclassified') AS loyaltysegment, COUNT(DISTINCT o.id) AS total_orders FROM \"{database}\".filtered_orders o JOIN latest_segments s ON o.customer = s.customer JOIN top_5_products t5 ON o.item_id = t5.item_id WHERE o.item_price > 0 AND o.organization = '{orgId}' GROUP BY o.item_name, o.item_id, COALESCE(NULLIF(s.segment, ''), 'Unclassified') ORDER BY total_orders DESC LIMIT 3;",
				dataStructure: "{\"item_name\": { \"type\": \"UTF8\", \"label\": \"Item Name\", \"prefix\": \"\"},\"item_id\": { \"type\": \"INT64\", \"label\": \"Item ID\", \"prefix\": \"\"},\"loyaltysegment\": { \"type\": \"UTF8\", \"label\": \"Loyalty Segment\", \"prefix\": \"\"},\"total_orders\": { \"type\": \"INT64\", \"label\": \"Total Orders\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 6900,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: true,
				description: "Identifies the most popular products among new customers across different loyalty segments, helping understand initial purchase preferences and product entry points."
			},
			{
				name: "discounts_purchase_number",
				query: "WITH all_orders AS ( SELECT id, customer, created_at, total_discounts FROM \"{database}\".\"filtered_orders\" WHERE organization = '{orgId}' ), full_numbered_orders AS ( SELECT id, customer, created_at, total_discounts, row_number() OVER ( PARTITION BY customer ORDER BY created_at ) AS purchase_number FROM all_orders ), analysis_orders AS ( SELECT * FROM full_numbered_orders WHERE created_at >= date_add('month', -3, current_date) ), order_discount_mapping AS ( SELECT d.order_id, CASE WHEN TRIM(d.discount_code) = '' THEN 'OTHER' ELSE d.discount_code END AS discount_code FROM \"{database}\".\"order_discounts\" d WHERE d.organization = '{orgId}' ), distinct_order_discounts AS ( SELECT ao.id, ao.purchase_number, odm.discount_code FROM analysis_orders ao JOIN order_discount_mapping odm ON odm.order_id = ao.id ), discount_usage AS ( SELECT purchase_number, discount_code, COUNT(DISTINCT id) AS orders_with_discount FROM distinct_order_discounts GROUP BY purchase_number, discount_code ), ranked_discount AS ( SELECT purchase_number, discount_code, orders_with_discount, row_number() OVER ( PARTITION BY purchase_number ORDER BY orders_with_discount DESC ) AS rn FROM discount_usage ), grouped_discount AS ( SELECT purchase_number, CASE WHEN rn <= 10 AND orders_with_discount > 1 THEN discount_code ELSE 'OTHER' END AS discount_group, orders_with_discount FROM ranked_discount ), final_aggregation AS ( SELECT purchase_number, discount_group, SUM(orders_with_discount) AS total_orders_with_discount FROM grouped_discount GROUP BY purchase_number, discount_group ), overall_counts AS ( SELECT purchase_number, COUNT(DISTINCT id) AS total_orders FROM analysis_orders GROUP BY purchase_number ) SELECT f.purchase_number, f.discount_group, f.total_orders_with_discount, ROUND( f.total_orders_with_discount * 100.0 / o.total_orders, 2 ) AS discount_percent FROM final_aggregation f JOIN overall_counts o ON f.purchase_number = o.purchase_number WHERE f.purchase_number <= 3 ORDER BY f.purchase_number, discount_percent DESC;",
				dataStructure: "{\"purchase_number\": {\"type\": \"INT64\", \"label\": \"Purchase Number\", \"prefix\": \"\"}, \"discount_group\": {\"type\": \"STRING\", \"label\": \"Discount Group\", \"prefix\": \"\"}, \"total_orders_with_discount\": {\"type\": \"INT64\", \"label\": \"Total Orders with Discount\", \"prefix\": \"\"}, \"discount_percent\": {\"type\": \"DOUBLE\", \"label\": \"Discount Percentage\", \"prefix\": \"%\"}}",
				defaultRunFrequency: "week",
				priority: 6001,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes discount usage patterns across customer purchase sequences, showing how discount effectiveness varies between first-time and repeat purchases."
			},
			{
				name: "ltv_new_customer",
				query: "WITH distinct_orders AS ( SELECT DISTINCT customer, id, total_price, created_at, DATE_TRUNC('month', created_at) as order_month FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND cancel_reason = '' AND total_price > 0    AND DATE_TRUNC('month', created_at) >= DATE_ADD('month', -12, DATE_TRUNC('month', CURRENT_DATE)) ), first_purchases AS ( SELECT customer, MIN(created_at) as first_purchase_date, DATE_TRUNC('month', MIN(created_at)) as cohort_month FROM distinct_orders GROUP BY customer ), cohort_customers AS ( SELECT f.customer, f.first_purchase_date, f.cohort_month FROM first_purchases f WHERE f.first_purchase_date >= DATE_ADD('day', -60, f.cohort_month) AND f.first_purchase_date < DATE_ADD('month', 1, f.cohort_month) ), customer_ltv AS ( SELECT cc.customer, cc.cohort_month, cc.first_purchase_date, MIN(d.created_at) as earliest_included_order, MAX(d.created_at) as latest_included_order, DATE_ADD('day', 60, cc.first_purchase_date) as cutoff_date, SUM(d.total_price) as lifetime_value, COUNT(DISTINCT d.id) as number_of_orders FROM cohort_customers cc INNER JOIN distinct_orders d ON cc.customer = d.customer AND d.created_at >= cc.first_purchase_date AND d.created_at < DATE_ADD('day', 60, cc.first_purchase_date) GROUP BY cc.customer, cc.cohort_month, cc.first_purchase_date ) SELECT cohort_month, COUNT(DISTINCT customer) as cohort_size, AVG(lifetime_value) as average_ltv, MIN(lifetime_value) as min_ltv, MAX(lifetime_value) as max_ltv, AVG(number_of_orders) as avg_orders_per_customer, SUM(lifetime_value) as total_cohort_revenue FROM customer_ltv GROUP BY cohort_month ORDER BY cohort_month DESC;\r\n",
				variables: "{\"groupByField\":\"cohort_month\"}",
				dataStructure: "{ \"cohort_month\": { \"type\": \"datetime\" }, \"cohort_size\": { \"type\": \"INT64\" }, \"average_ltv\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"min_ltv\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"max_ltv\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"avg_orders_per_customer\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"total_cohort_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" } }",
				defaultRunFrequency: "month",
				priority: 6800,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates customer lifetime value (LTV) metrics for new customers by cohort, including average order value and total revenue, to track customer value development over time."
			},
			{
				name: "custom_donahue_fit",
				type: "shop-orders-variants",
				defaultRunFrequency: "week",
				priority: 6500,
				isDefault: false,
				includeInPrompt: false,
				description: "Custom analysis of product fit preferences and sizing patterns to optimize inventory and reduce returns."
			},
			{
				name: "klaviyo_campaign_classifier",
				type: "klaviyo-campaign-classifier",
				defaultRunFrequency: "week",
				priority: 7501,
				featureId: "ai-strategist",
				isDefault: true,
				includeInPrompt: false,
				description: "Classifies and analyzes Klaviyo email campaign performance to identify most effective messaging and targeting strategies."
			},
			{
				name: "klaviyo_campaigns",
				type: "klaviyo-data",
				defaultRunFrequency: "week",
				featureId: "ai-strategist",
				priority: 7500,
				isDefault: true,
				includeInPrompt: false,
				dataStructure: "{ \"id\": { \"type\": \"UTF8\" }, \"type\": { \"type\": \"UTF8\" }, \"name\": { \"type\": \"UTF8\" }, \"status\": { \"type\": \"UTF8\" }, \"archived\": { \"type\": \"boolean\" }, \"audiences_included\": { \"type\": \"UTF8\" }, \"audiences_excluded\": { \"type\": \"UTF8\" }, \"send_options_use_smart_sending\": { \"type\": \"boolean\" }, \"send_options_ignore_unsubscribes\": { \"type\": \"boolean\" }, \"tracking_options_add_tracking_params\": { \"type\": \"boolean\" }, \"tracking_options_custom_tracking_params\": { \"type\": \"UTF8\" }, \"tracking_options_is_tracking_clicks\": { \"type\": \"boolean\" }, \"tracking_options_is_tracking_opens\": { \"type\": \"boolean\" }, \"send_strategy_method\": { \"type\": \"UTF8\" }, \"send_strategy_options_static_datetime\": { \"type\": \"UTF8\" }, \"send_strategy_options_static_is_local\": { \"type\": \"boolean\" }, \"send_strategy_options_static_send_past_recipients_immediately\": { \"type\": \"boolean\" }, \"send_strategy_options_throttled\": { \"type\": \"double\" }, \"send_strategy_options_sto\": { \"type\": \"double\" }, \"created_at\": { \"type\": \"UTF8\" }, \"scheduled_at\": { \"type\": \"UTF8\" }, \"updated_at\": { \"type\": \"UTF8\" }, \"send_time\": { \"type\": \"UTF8\" }, \"messages\": { \"type\": \"UTF8\" }, \"stats_average_order_value\": { \"type\": \"double\" }, \"stats_bounce_rate\": { \"type\": \"double\" }, \"stats_bounced\": { \"type\": \"double\" }, \"stats_bounced_or_failed\": { \"type\": \"double\" }, \"stats_bounced_or_failed_rate\": { \"type\": \"double\" }, \"stats_click_rate\": { \"type\": \"double\" }, \"stats_click_to_open_rate\": { \"type\": \"double\" }, \"stats_clicks\": { \"type\": \"double\" }, \"stats_clicks_unique\": { \"type\": \"double\" }, \"stats_conversion_rate\": { \"type\": \"double\" }, \"stats_conversion_uniques\": { \"type\": \"double\" }, \"stats_conversion_value\": { \"type\": \"double\" }, \"stats_conversions\": { \"type\": \"double\" }, \"stats_delivered\": { \"type\": \"double\" }, \"stats_delivery_rate\": { \"type\": \"double\" }, \"stats_failed\": { \"type\": \"double\" }, \"stats_failed_rate\": { \"type\": \"double\" }, \"stats_open_rate\": { \"type\": \"double\" }, \"stats_opens\": { \"type\": \"double\" }, \"stats_opens_unique\": { \"type\": \"double\" }, \"stats_recipients\": { \"type\": \"double\" }, \"stats_revenue_per_recipient\": { \"type\": \"double\" }, \"stats_spam_complaint_rate\": { \"type\": \"double\" }, \"stats_spam_complaints\": { \"type\": \"double\" }, \"stats_unsubscribe_rate\": { \"type\": \"double\" }, \"stats_unsubscribe_uniques\": { \"type\": \"double\" }, \"stats_unsubscribes\": { \"type\": \"double\" }, \"send_strategy_options_static\": { \"type\": \"double\" }, \"send_strategy_options_sto_date\": { \"type\": \"UTF8\" }, \"send_strategy_options_throttled_datetime\": { \"type\": \"UTF8\" }, \"send_strategy_options_throttled_throttle_percentage\": { \"type\": \"double\" } }",
				description: "Tracks comprehensive Klaviyo campaign metrics including engagement rates, conversion data, and revenue impact for email marketing optimization."
			},
			{
				name: "klaviyo_flows",
				type: "klaviyo-data",
				defaultRunFrequency: "week",
				featureId: "ai-strategist",
				priority: 8500,
				isDefault: true,
				includeInPrompt: false,
				dataStructure: "{ \"id\": { \"type\": \"UTF8\" }, \"type\": { \"type\": \"UTF8\" }, \"name\": { \"type\": \"UTF8\" }, \"status\": { \"type\": \"UTF8\" }, \"archived\": { \"type\": \"boolean\" }, \"created\": { \"type\": \"UTF8\" }, \"updated\": { \"type\": \"UTF8\" }, \"trigger_type\": { \"type\": \"UTF8\" }, \"stats_average_order_value\": { \"type\": \"double\" }, \"stats_bounce_rate\": { \"type\": \"double\" }, \"stats_bounced\": { \"type\": \"double\" }, \"stats_bounced_or_failed\": { \"type\": \"double\" }, \"stats_bounced_or_failed_rate\": { \"type\": \"double\" }, \"stats_click_rate\": { \"type\": \"double\" }, \"stats_click_to_open_rate\": { \"type\": \"double\" }, \"stats_clicks\": { \"type\": \"double\" }, \"stats_clicks_unique\": { \"type\": \"double\" }, \"stats_conversion_rate\": { \"type\": \"double\" }, \"stats_conversion_uniques\": { \"type\": \"double\" }, \"stats_conversion_value\": { \"type\": \"double\" }, \"stats_conversions\": { \"type\": \"double\" }, \"stats_delivered\": { \"type\": \"double\" }, \"stats_delivery_rate\": { \"type\": \"double\" }, \"stats_failed\": { \"type\": \"double\" }, \"stats_failed_rate\": { \"type\": \"double\" }, \"stats_open_rate\": { \"type\": \"double\" }, \"stats_opens\": { \"type\": \"double\" }, \"stats_opens_unique\": { \"type\": \"double\" }, \"stats_recipients\": { \"type\": \"double\" }, \"stats_revenue_per_recipient\": { \"type\": \"double\" }, \"stats_spam_complaint_rate\": { \"type\": \"double\" }, \"stats_spam_complaints\": { \"type\": \"double\" }, \"stats_unsubscribe_rate\": { \"type\": \"double\" }, \"stats_unsubscribe_uniques\": { \"type\": \"double\" }, \"stats_unsubscribes\": { \"type\": \"double\" }}",
				description: "Monitors automated email flow performance in Klaviyo, analyzing conversion rates and revenue impact of different automation sequences."
			},
			{
				name: "klaviyo_segments",
				type: "klaviyo-data",
				defaultRunFrequency: "week",
				featureId: "ai-strategist",
				priority: 9500,
				isDefault: true,
				includeInPrompt: false,
				dataStructure: "{ \"id\": { \"type\": \"UTF8\" }, \"type\": { \"type\": \"UTF8\" }, \"name\": { \"type\": \"UTF8\" }, \"definition_condition_groups\": { \"type\": \"UTF8\" }, \"created\": { \"type\": \"UTF8\" }, \"updated\": { \"type\": \"UTF8\" }, \"is_active\": { \"type\": \"boolean\" }, \"is_processing\": { \"type\": \"boolean\" }, \"is_starred\": { \"type\": \"boolean\" }, \"stats_net_members_changed\": { \"type\": \"double\" }, \"stats_members_added\": { \"type\": \"double\" }, \"stats_members_removed\": { \"type\": \"double\" }, \"stats_total_members\": { \"type\": \"double\" }, \"rundate\": { \"type\": \"bigint\" } }",
				description: "Analyzes customer segment composition and behavior in Klaviyo to optimize targeting and personalization strategies."
			},
			{
				name: "excluded_orders",
				type: "excluded-orders",
				defaultRunFrequency: "day",
				priority: 110,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Identifies and tracks orders that should be excluded from analysis due to specific criteria, ensuring data accuracy."
			},
			{
				name: "has_abandoned_checkout",
				type: "default-postgres",
				query: "WITH abandoned_carts AS (SELECT DISTINCT customerid FROM posgresreadreplicav3.public.cartdata WHERE orgid = {orgId} AND status != 'completed' AND lastupdatetime < date_add('day', -1, current_timestamp) AND lastupdatetime > date_add('month', -1, current_timestamp)) SELECT c.customerid as customer, CASE WHEN ac.customerid IS NOT NULL THEN 1 ELSE 0 END as has_abandoned FROM (SELECT DISTINCT customerid FROM posgresreadreplicav3.public.cartdata WHERE orgid = {orgId} and customerid != '') c LEFT JOIN abandoned_carts ac ON c.customerid = ac.customerid;",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"customer\": {\"type\": \"UTF8\"}, \"has_abandoned\": {\"type\": \"INT64\"}}",
				defaultRunFrequency: "day",
				priority: 3069,
				fieldMappings: "{\"has_abandoned_checkout\": \"has_abandoned\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks customers with abandoned checkouts in the last month to identify recovery opportunities and analyze cart abandonment patterns."
			},
			{
				name: "engagement",
				type: "default-postgres",
				query: "WITH parsed_events AS ( SELECT COALESCE(customer, JSON_EXTRACT_SCALAR(data, '$.raleonid')) AS identifier, JSON_EXTRACT_SCALAR(data, '$.raleonid') AS raleonid, JSON_EXTRACT_SCALAR(data, '$.shopid') AS shopid, JSON_EXTRACT_SCALAR(data, '$.page') AS page, customer, DATE(timestamp) AS view_date, CASE WHEN JSON_EXTRACT_SCALAR(data, '$.page') = 'products' THEN 2.0 WHEN JSON_EXTRACT_SCALAR(data, '$.page') = 'collections' THEN 1.5 ELSE 1.0 END AS page_weight FROM \"loyalty_events\".events LEFT JOIN \"{database}\".excluded_customers ec ON COALESCE(customer, JSON_EXTRACT_SCALAR(data, '$.raleonid')) = ec.customerid AND ec.organization = '{orgId}' WHERE events.organization = '{orgId}' AND event = 'website_visited' AND ec.customerid IS NULL AND CAST(CONCAT(year, '-', month, '-', day) AS DATE) BETWEEN DATE_ADD('day', -90, CURRENT_DATE) AND CURRENT_DATE ), raleon_to_customer AS ( SELECT raleonid, MAX(customer) AS customer FROM parsed_events WHERE raleonid IS NOT NULL AND customer IS NOT NULL GROUP BY raleonid ), distinct_orders AS ( SELECT DISTINCT customer, id AS order_id, created_at FROM \"{database}\".filtered_orders LEFT JOIN \"{database}\".excluded_customers ec ON filtered_orders.customer = ec.customerid AND ec.organization = '{orgId}' WHERE filtered_orders.organization = '{orgId}' AND ec.customerid IS NULL AND created_at BETWEEN DATE_ADD('day', -90, CURRENT_DATE) AND CURRENT_DATE ), orders_aggregated AS ( SELECT customer, COUNT(*) FILTER (WHERE created_at BETWEEN DATE_ADD('day', -90, CURRENT_DATE) AND CURRENT_DATE) * 2 AS order_weight_90, COUNT(*) FILTER (WHERE created_at BETWEEN DATE_ADD('day', -60, CURRENT_DATE) AND CURRENT_DATE) * 2 AS order_weight_60, COUNT(*) FILTER (WHERE created_at BETWEEN DATE_ADD('day', -30, CURRENT_DATE) AND CURRENT_DATE) * 2 AS order_weight_30 FROM distinct_orders GROUP BY customer ), consolidated_views AS ( SELECT COALESCE(rc.customer, e.customer, e.raleonid) AS final_identifier, SUM(e.page_weight) FILTER (WHERE e.view_date BETWEEN DATE_ADD('day', -90, CURRENT_DATE) AND CURRENT_DATE) AS weighted_view_count_90, SUM(e.page_weight) FILTER (WHERE e.view_date BETWEEN DATE_ADD('day', -60, CURRENT_DATE) AND CURRENT_DATE) AS weighted_view_count_60, SUM(e.page_weight) FILTER (WHERE e.view_date BETWEEN DATE_ADD('day', -30, CURRENT_DATE) AND CURRENT_DATE) AS weighted_view_count_30 FROM parsed_events e LEFT JOIN raleon_to_customer rc ON e.raleonid = rc.raleonid WHERE COALESCE(rc.customer, e.customer, e.raleonid) IS NOT NULL AND COALESCE(rc.customer, e.customer, e.raleonid) != '' GROUP BY COALESCE(rc.customer, e.customer, e.raleonid) ), views_and_orders AS ( SELECT COALESCE(cv.final_identifier, o.customer) AS identifier, COALESCE(cv.weighted_view_count_90, 0) + COALESCE(o.order_weight_90, 0) AS total_weighted_engagement_90, COALESCE(cv.weighted_view_count_60, 0) + COALESCE(o.order_weight_60, 0) AS total_weighted_engagement_60, COALESCE(cv.weighted_view_count_30, 0) + COALESCE(o.order_weight_30, 0) AS total_weighted_engagement_30 FROM consolidated_views cv FULL OUTER JOIN orders_aggregated o ON cv.final_identifier = o.customer ), scaled_views AS ( SELECT identifier, total_weighted_engagement_90, total_weighted_engagement_60, total_weighted_engagement_30, CAST( 1000.0 * (total_weighted_engagement_90 - MIN(total_weighted_engagement_90) OVER()) \/ NULLIF(MAX(total_weighted_engagement_90) OVER() - MIN(total_weighted_engagement_90) OVER(), 0) AS INT ) AS scaled_score_90, CAST( 1000.0 * (total_weighted_engagement_60 - MIN(total_weighted_engagement_60) OVER()) \/ NULLIF(MAX(total_weighted_engagement_60) OVER() - MIN(total_weighted_engagement_60) OVER(), 0) AS INT ) AS scaled_score_60, CAST( 1000.0 * (total_weighted_engagement_30 - MIN(total_weighted_engagement_30) OVER()) \/ NULLIF(MAX(total_weighted_engagement_30) OVER() - MIN(total_weighted_engagement_30) OVER(), 0) AS INT ) AS scaled_score_30 FROM views_and_orders WHERE identifier IS NOT NULL ), latest_rundate AS ( SELECT MAX(rundate) AS latest_rundate FROM \"{database}\".engagement WHERE organization = '{orgId}' ), engagement_data AS ( SELECT e.customer FROM \"{database}\".engagement e CROSS JOIN latest_rundate lr WHERE e.rundate = lr.latest_rundate AND e.organization = '{orgId}' ), combined_results AS ( SELECT COALESCE(q.identifier, ed.customer) AS customer, COALESCE(q.scaled_score_90, 0) AS engagement_90_days, COALESCE(q.scaled_score_60, 0) AS engagement_60_days, COALESCE(q.scaled_score_30, 0) AS engagement_30_days FROM scaled_views q FULL OUTER JOIN engagement_data ed ON q.identifier = ed.customer ) SELECT customer, engagement_90_days, engagement_60_days, engagement_30_days FROM combined_results ORDER BY engagement_90_days DESC;",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"engagement_30_days\": { \"type\": \"INT64\"},\"engagement_60_days\": { \"type\": \"INT64\"},\"engagement_90_days\": { \"type\": \"INT64\"}}",
				defaultRunFrequency: "week",
				priority: 4900,
				fieldMappings: "{\"engagement30days\": \"engagement_30_days\",\"engagement60days\": \"engagement_60_days\",\"engagement90days\": \"engagement_90_days\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Measures customer engagement levels across multiple timeframes using weighted interactions and purchase behavior."
			},
			{
				name: "email_opens",
				type: "default-postgres",
				query: "WITH email_windows AS (SELECT json_extract_scalar(data, '$.raleonuserid') as customer, json_extract_scalar(data, '$.time') as email_id, timestamp, CASE WHEN timestamp >= date_add('day', -30, CURRENT_DATE) THEN '30' ELSE NULL END as window_30, CASE WHEN timestamp >= date_add('day', -60, CURRENT_DATE) THEN '60' ELSE NULL END as window_60, CASE WHEN timestamp >= date_add('day', -90, CURRENT_DATE) THEN '90' ELSE NULL END as window_90 FROM \"AwsDataCatalog\".loyalty_events.events WHERE event = 'email_open' AND organization = '{orgId}'), metrics AS (SELECT customer, COUNT(*) FILTER (WHERE window_30 IS NOT NULL) as total_opens_30, COUNT(*) FILTER (WHERE window_60 IS NOT NULL) as total_opens_60, COUNT(*) FILTER (WHERE window_90 IS NOT NULL) as total_opens_90, COUNT(DISTINCT CASE WHEN window_30 IS NOT NULL THEN email_id END) as unique_opens_30, COUNT(DISTINCT CASE WHEN window_60 IS NOT NULL THEN email_id END) as unique_opens_60, COUNT(DISTINCT CASE WHEN window_90 IS NOT NULL THEN email_id END) as unique_opens_90 FROM email_windows GROUP BY customer) SELECT customer, COALESCE(unique_opens_30, 0) as email_unique_opensL30, COALESCE(unique_opens_60, 0) as email_unique_opensL60, COALESCE(unique_opens_90, 0) as email_unique_opensL90, COALESCE(total_opens_30, 0) as email_total_opensL30, COALESCE(total_opens_60, 0) as email_total_opensL60, COALESCE(total_opens_90, 0) as email_total_opensL90 FROM metrics WHERE customer IS NOT NULL",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"email_unique_opensL30\": { \"type\": \"INT64\" },\"email_unique_opensL60\": { \"type\": \"INT64\" },\"email_unique_opensL90\": { \"type\": \"INT64\" },\"email_total_opensL30\": { \"type\": \"INT64\" },\"email_total_opensL60\": { \"type\": \"INT64\" },\"email_total_opensL90\": { \"type\": \"INT64\" }}",
				defaultRunFrequency: "day",
				priority: 7000,
				fieldMappings: "{\"email_unique_opensl30\": \"email_unique_opensL30\", \"email_unique_opensl60\": \"email_unique_opensL60\", \"email_unique_opensl90\": \"email_unique_opensL90\", \"email_total_opensl30\": \"email_total_opensL30\", \"email_total_opensl60\": \"email_total_opensL60\", \"email_total_opensl90\": \"email_total_opensL90\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks email engagement metrics including unique and total opens across 30, 60, and 90-day periods to measure communication effectiveness."
			},
			{
				name: "klaviyo_sync_attributes",
				type: "klaviyo-referral",
				query: "WITH user_attributes AS ( SELECT raleonuseridentityid, MAX( CASE WHEN key = 'fit_type' THEN value END ) as fit_type FROM public.raleonuseridentityattributes GROUP BY raleonuseridentityid ) SELECT \"raleonuseridentity\".identityvalue, \"raleonuseridentity\".referralcode, \"raleonuseridentity\".ltvdistribution, \"raleonuseridentity\".ltv, \"raleonuseridentity\".revenue, \"raleonuseridentity\".replenishmentscore, \"raleonuseridentity\".churnrisk, \"raleonuseridentity\".refundpropensity, \"raleonuseridentity\".discountscore, \"raleonuseridentity\".identityvalue as identityvaluesync, \"raleonuseridentity\".loyaltysegment, \"raleonuseridentity\".aov, \"raleonuseridentity\".totalorders, \"raleonuseridentity\".totalrefunds, \"raleonuseridentity\".replenishmentproduct, \"raleonuseridentity\".dayssincelastsubscription, \"raleonuseridentity\".engagement30days, \"raleonuseridentity\".winbackscore, \"raleonuseridentity\".rebuypropensity, \"raleonuseridentity\".rebuyproduct, \"raleonuseridentity\".has_abandoned_checkout, \"raleonuseridentity\".email_unique_opensl30, \"raleonuseridentity\".email_unique_opensl60, \"raleonuseridentity\".email_unique_opensl90, \"raleonuseridentity\".email_total_opensl30, \"raleonuseridentity\".email_total_opensl60, \"raleonuseridentity\".email_total_opensl90, \"raleonuseridentity\".engagement60days, \"raleonuseridentity\".engagement90days, \"raleonuseridentity\".subscriptionpropensity, \"raleonuseridentity\".raleonuserid, ua.fit_type FROM \"public\".\"raleonuseridentity\" LEFT JOIN user_attributes ua ON ua.raleonuseridentityid = \"raleonuseridentity\".id WHERE \"raleonuseridentity\".orgid = {orgId};",
				catalog: "posgresreadreplicav3",
				dataStructure: "{ \"identityvalue\": { \"type\": \"UTF8\" }, \"referralcode\": { \"type\": \"UTF8\" }, \"ltvdistribution\": { \"type\": \"UTF8\" }, \"ltv\": { \"type\": \"UTF8\" }, \"revenue\": { \"type\": \"UTF8\" }, \"replenishmentscore\": { \"type\": \"UTF8\" }, \"churnrisk\": { \"type\": \"UTF8\" }, \"refundpropensity\": { \"type\": \"UTF8\" }, \"discountscore\": { \"type\": \"UTF8\" }, \"identityvaluesync\": { \"type\": \"UTF8\" }, \"loyaltysegment\": { \"type\": \"UTF8\" }, \"aov\": { \"type\": \"UTF8\" }, \"totalorders\": { \"type\": \"UTF8\" }, \"totalrefunds\": { \"type\": \"UTF8\" }, \"replenishmentproduct\": { \"type\": \"UTF8\" }, \"dayssincelastsubscription\": { \"type\": \"UTF8\" }, \"engagement30days\": { \"type\": \"UTF8\" }, \"winbackscore\": { \"type\": \"UTF8\" }, \"rebuypropensity\": { \"type\": \"UTF8\" }, \"rebuyproduct\": { \"type\": \"UTF8\" }, \"has_abandoned_checkout\": { \"type\": \"UTF8\" }, \"email_unique_opensl30\": { \"type\": \"UTF8\" }, \"email_unique_opensl60\": { \"type\": \"UTF8\" }, \"email_unique_opensl90\": { \"type\": \"UTF8\" }, \"email_total_opensl30\": { \"type\": \"UTF8\" }, \"email_total_opensl60\": { \"type\": \"UTF8\" }, \"email_total_opensl90\": { \"type\": \"UTF8\" }, \"engagement60days\": { \"type\": \"UTF8\" }, \"engagement90days\": { \"type\": \"UTF8\" }, \"subscriptionpropensity\": { \"type\": \"UTF8\" }, \"raleonuserid\": { \"type\": \"UTF8\" }, \"fit_type\": { \"type\": \"UTF8\" } }",
				defaultRunFrequency: "day",
				priority: 9999999,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Synchronizes customer attributes and metrics with Klaviyo for enhanced segmentation and personalization capabilities."
			},
			{
				name: "winback_score",
				type: "default-postgres",
				query: "WITH WinbackPotential AS (SELECT customer, previoussegment, rundate FROM {database}.rfm_segments WHERE organization = '{orgId}' AND rundate = (SELECT MAX(rundate) FROM {database}.rfm_segments r2 WHERE r2.customer = {database}.rfm_segments.customer)), LastVeryLoyal AS (SELECT customer, MAX(rundate) as last_very_loyal_date FROM {database}.rfm_segments WHERE organization = '{orgId}' AND previoussegment = 'Very Loyal' GROUP BY customer), FINAL_DATA AS (SELECT wp.customer, CASE WHEN wp.previoussegment = 'Very Loyal' THEN 0 WHEN NOT EXISTS (SELECT 1 FROM LastVeryLoyal lv WHERE wp.customer = lv.customer) THEN 0 ELSE GREATEST(LEAST(CAST(1000 * (1 - (DATE_DIFF('day', lv.last_very_loyal_date, wp.rundate) \/ 180.0)) AS INTEGER), 1000), 0) END as winback_score FROM WinbackPotential wp LEFT JOIN LastVeryLoyal lv ON wp.customer = lv.customer) SELECT * FROM FINAL_DATA",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"winback_score\": { \"type\": \"INT64\"}}",
				defaultRunFrequency: "week",
				priority: 3010,
				fieldMappings: "{\"winbackscore\": \"winback_score\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates a score indicating the potential for winning back previously loyal customers based on their purchase history and segment transitions."
			},
			{
				name: "subscription_propensity",
				type: "subscription-propensity",
				catalog: "posgresreadreplicav3",
				defaultRunFrequency: "week",
				priority: 6000,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Predicts customer likelihood to subscribe to recurring purchases based on purchase patterns and product preferences."
			},
			{
				name: "rebuy_propensity",
				type: "rebuy-propensity",
				catalog: "posgresreadreplicav3",
				defaultRunFrequency: "week",
				priority: 3002,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Predicts likelihood of customers making repeat purchases based on historical purchase patterns and engagement metrics."
			},
			{
				name: "user_ltv_revenue",
				type: "user-ltv-revenue",
				defaultRunFrequency: "week",
				priority: 3800,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates and tracks customer lifetime value based on purchase history and revenue contribution."
			},
			{
				name: "replenish_score",
				type: "replenish-score",
				defaultRunFrequency: "week",
				priority: 4500,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes purchase intervals to identify products and customers with regular replenishment patterns."
			},
			{
				name: "total_orders",
				type: "default-postgres",
				query: "WITH excluded_customers AS ( SELECT customerid AS excluded_customer FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ), aggregated_refunds AS ( SELECT order_id, COUNT(*) AS refund_count FROM \"{database}\".\"order_refunds\" WHERE organization = '{orgId}' GROUP BY order_id ), aggregated_orders AS ( SELECT o.id AS order_id, o.customer AS identityvalue, o.organization FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.customer NOT IN ( SELECT excluded_customer FROM excluded_customers ) GROUP BY o.id, o.customer, o.organization ) SELECT o.identityvalue, o.organization, COUNT(DISTINCT o.order_id) AS total_orders, COALESCE(SUM(r.refund_count), 0) AS total_refunds FROM aggregated_orders o LEFT JOIN aggregated_refunds r ON o.order_id = r.order_id GROUP BY o.identityvalue, o.organization;",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"organization\": {\"type\": \"UTF8\"},\"total_orders\": { \"type\": \"INT64\"},\"total_refunds\": {\"type\": \"INT64\"}}",
				defaultRunFrequency: "week",
				priority: 4000,
				fieldMappings: "{\"totalorders\": \"total_orders\",\"totalrefunds\": \"total_refunds\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks total order count and refund history per customer to measure purchase frequency and satisfaction."
			},
			{
				name: "days_since_subscription",
				type: "default-postgres",
				query: "WITH excluded_customers AS ( SELECT customerid AS excluded_customer FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ), distinct_subscription_orders AS ( SELECT DISTINCT customer, id AS order_id, created_at FROM \"{database}\".filtered_orders WHERE is_subscription = true AND organization = '{orgId}' AND customer NOT IN (SELECT excluded_customer FROM excluded_customers) ), latest_subscription_orders AS ( SELECT customer, MAX(created_at) AS last_subscription_date FROM distinct_subscription_orders GROUP BY customer ) SELECT customer, DATE_DIFF('day', last_subscription_date, CURRENT_DATE) AS days_since_last_subscription FROM latest_subscription_orders;",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"days_since_last_subscription\": { \"type\": \"INT64\"}}",
				defaultRunFrequency: "week",
				priority: 4100,
				fieldMappings: "{\"dayssincelastsubscription\": \"days_since_last_subscription\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Monitors time elapsed since last subscription activity to identify at-risk subscriptions and renewal opportunities."
			},
			{
				name: "churn_risk",
				type: "churn-risk",
				defaultRunFrequency: "week",
				priority: 5500,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates customer churn risk based on purchase patterns, engagement levels, and historical behavior."
			},
			{
				name: "refund_propensity",
				type: "refund-propensity",
				defaultRunFrequency: "week",
				priority: 3900,
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Predicts likelihood of product returns or refund requests based on customer and product history."
			},
			{
				name: "discount_seeker",
				type: "default-postgres",
				query: "WITH distinct_orders AS ( SELECT customer, id AS order_id, CASE WHEN EXISTS ( SELECT 1 FROM \"{database}\".order_discounts d WHERE d.order_id = o.id AND d.organization = '{orgId}' ) THEN 1 ELSE 0 END AS has_coupon, COALESCE(( SELECT SUM(discount_amount) FROM \"{database}\".order_discounts d WHERE d.order_id = o.id AND d.organization = '{orgId}' ), 0) AS discount_amount FROM \"{database}\".filtered_orders o WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerid FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), customer_orders AS ( SELECT customer, COUNT(order_id) AS total_orders, SUM(has_coupon) AS orders_with_coupon, SUM(discount_amount) AS total_saved FROM distinct_orders GROUP BY customer ) SELECT customer, CASE WHEN orders_with_coupon > 0 THEN (0.6 + 0.4 * (orders_with_coupon \/ total_orders)) * 1000 WHEN total_orders = 1 THEN 0.5 * 1000 WHEN orders_with_coupon = 0 AND total_orders > 1 THEN 0.5 * EXP(-0.1 * total_orders) * 1000 ELSE 0 END AS discountscore FROM customer_orders ORDER BY discountscore DESC;",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"discountscore\": { \"type\": \"INT64\"}}",
				defaultRunFrequency: "week",
				priority: 4800,
				fieldMappings: "{\"discountscore\": \"discountscore\"}",
				featureId: "ai-segments",
				isDefault: true,
				includeInPrompt: false,
				description: "Identifies customers who frequently use discounts to help optimize promotion strategies and prevent discount abuse."
			},
			{
				name: "gwp_metrics",
				query: "WITH GiftsAdded AS ( SELECT COUNT( DISTINCT COALESCE( json_extract_scalar(data, '$.cartToken'), json_extract_scalar(data, '$.carttoken') ) ) AS gifts_added_to_cart FROM \"loyalty_events\".\"events\" WHERE event = 'free-gift-added' AND organization = '{orgId}' ), GiftsRedeemed AS ( SELECT COUNT(DISTINCT order_id) AS gifts_redeemed FROM \"{database}\".\"order_discounts\" WHERE organization = '{orgId}' AND discount_type = 'free-gift' ), RevenueWithGWP AS ( SELECT SUM(total_price) AS revenue_with_gwp FROM ( SELECT DISTINCT ord.id AS order_id, ord.total_price FROM \"{database}\".\"order_discounts\" AS discounts JOIN \"{database}\".\"filtered_orders\" AS ord ON discounts.order_id = ord.id WHERE discounts.organization = '{orgId}' AND discounts.discount_type = 'free-gift' AND ord.organization = '{orgId}' AND ord.customer NOT IN ( SELECT customerid FROM \"{database}\".\"excluded_customers\" WHERE organization = '{orgId}' ) ) AS UniqueOrders ) SELECT GiftsAdded.gifts_added_to_cart, GiftsRedeemed.gifts_redeemed, COALESCE( CAST(GiftsRedeemed.gifts_redeemed AS DOUBLE) \/ NULLIF(CAST(GiftsAdded.gifts_added_to_cart AS DOUBLE), 0), 0 )*100 AS gwp_conversion_rate, RevenueWithGWP.revenue_with_gwp FROM GiftsAdded, GiftsRedeemed, RevenueWithGWP;",
				dataStructure: "{\"gifts_added_to_cart\": {\"type\": \"INT64\", \"label\": \"Gifts Added\", \"prefix\": \"\"},\"gifts_redeemed\": {\"type\": \"INT64\", \"label\": \"Gifts Redeemed\", \"prefix\": \"\"},\"gwp_conversion_rate\": {\"type\": \"DOUBLE\", \"label\": \"Gift Conversion Rate\", \"suffix\": \"%\"},\"revenue_with_gwp\": {\"type\": \"DOUBLE\", \"label\": \"Gift Revenue\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 1580,
				featureId: "gwp-features",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes Gift With Purchase (GWP) program performance including redemption rates and revenue impact."
			},
			{
				name: "vip_tier_top_coupons",
				query: "SELECT ic.name, count(ic.name) as used_count FROM public.organization o JOIN public.loyaltyProgram lp ON o.id = lp.orgId AND lp.active = true JOIN public.raleonuseridentity rui ON o.id = rui.orgId JOIN public.inventorycoupon ic ON ic.raleonuserid = rui.raleonuserid JOIN public.loyaltyrewarddefinition l ON ic.loyaltyrewarddefinitionid = l.id join public.viptier v on l.loyaltycampaignid = v.loyaltycampaignid WHERE ic.used = true and o.id={orgId} group by ic.name ORDER BY used_count DESC LIMIT 10",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"name\": {\"type\": \"UTF8\", \"label\": \"Coupon Name\", \"prefix\": \"\"},\"used_count\": {\"type\": \"INT64\", \"label\": \"Coupons Used\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 2860,
				featureId: "vip",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks most frequently used coupons across VIP tiers to optimize reward structures and measure program effectiveness."
			},
			{
				name: "vip_tier_metrics",
				query: "WITH customer_segments AS ( SELECT identityvalue AS customer, name AS segment FROM \"posgresreadreplicav3\".\"public\".raleonuseridentity r JOIN \"posgresreadreplicav3\".\"public\".viptier v ON r.viptierid = v.id WHERE orgid = {orgId} AND r.viptierid IS NOT NULL ), distinct_orders AS ( SELECT DISTINCT o.customer, o.id AS order_id, o.total_price AS order_total FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), customer_ltv AS ( SELECT customer, SUM(order_total) AS ltv FROM distinct_orders GROUP BY customer ), segment_ltv AS ( SELECT cs.segment, AVG(cl.ltv) AS avg_ltv FROM customer_segments cs JOIN customer_ltv cl ON cs.customer = cl.customer GROUP BY cs.segment ) SELECT cs.segment, SUM(aot.order_total) AS revenue, COUNT(aot.order_id) AS order_count, SUM(aot.order_total) \/ COUNT(aot.order_id) AS aov, sl.avg_ltv, COUNT(DISTINCT aot.customer) AS customer_count FROM distinct_orders aot JOIN customer_segments cs ON aot.customer = cs.customer JOIN segment_ltv sl ON cs.segment = sl.segment GROUP BY cs.segment, sl.avg_ltv  Order by customer_count Desc;",
				dataStructure: "{\"segment\": {\"type\": \"UTF8\", \"label\": \"Segment\", \"prefix\": \"%\"},\"revenue\": {\"type\": \"DOUBLE\", \"label\": \"Revenue\", \"prefix\": \"$\"},\"order_count\": {\"type\": \"INT64\", \"label\": \"Order Count\", \"prefix\": \"\"},\"aov\": {\"type\": \"DOUBLE\", \"label\": \"AOV\", \"prefix\": \"$\"},\"avg_ltv\": {\"type\": \"DOUBLE\", \"label\": \"LTV\", \"prefix\": \"$\"},\"customer_count\": {\"type\": \"INT64\", \"label\": \"Customer Count\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 2850,
				featureId: "vip",
				isDefault: true,
				includeInPrompt: false,
				description: "Measures performance metrics across VIP tiers including revenue, order counts, and customer value."
			},
			{
				name: "hubspot",
				type: "hubspot",
				query: "SELECT max_by(r.total_revenue, r.rundate) AS annualrevenue, 'raleon_platform' AS revenue_data_source, MAX(o.externaldomain) AS domain, MAX(o.uninstalleddate IS NULL) AS raleon_is_installed, MAX(o.shopifyconnecteddate) AS shopify_connected, MAX(lp.activationdate) AS loyalty_live_date, MAX(p.name) AS raleon_plan_name, MAX(op.status) AS raleon_plan_status, MAX(op.subscriptionid IS NOT NULL) AS raleon_plan_paid, MAX(COALESCE(op.priceoverride, p.price)) AS raleon_plan_amount, MAX(COALESCE(campaign_chats.cnt_campaign_chats, 0)) AS campaign_chats, MAX(COALESCE(plan_chats.cnt_plan_chats, 0)) AS plan_chats, MAX(COALESCE(segments_cnt.cnt_segments_synced, 0)) AS segments_synced, MAX(segments_latest.latest_segment_synced) AS latest_segment_synced, MAX(klaviyo_key.created_at) AS klaviyo_connected FROM \"{database}\".\"revenue\" r LEFT JOIN \"posgresreadreplica-dev\".\"public\".organization o ON o.id = CAST(r.organization AS INT) LEFT JOIN \"posgresreadreplica-dev\".\"public\".loyaltyprogram lp ON lp.orgid = CAST(r.organization AS INT) LEFT JOIN \"posgresreadreplica-dev\".\"public\".organizationplan op ON op.orgid = CAST(r.organization AS INT) LEFT JOIN \"posgresreadreplica-dev\".\"public\".plan p ON op.planid = p.id LEFT JOIN ( SELECT organizationid, COUNT(*) AS cnt_campaign_chats FROM \"posgresreadreplica-dev\".\"public\".conversation WHERE prompttemplateid in(10,12) GROUP BY organizationid ) campaign_chats ON campaign_chats.organizationid = CAST(r.organization AS INT) LEFT JOIN ( SELECT organizationid, COUNT(*) AS cnt_plan_chats FROM \"posgresreadreplica-dev\".\"public\".conversation WHERE prompttemplateid IN (11) GROUP BY organizationid ) plan_chats ON plan_chats.organizationid = CAST(r.organization AS INT) LEFT JOIN ( SELECT orgid, COUNT(*) AS cnt_segments_synced FROM \"posgresreadreplica-dev\".\"public\".organizationsegment WHERE externalid IS NOT NULL GROUP BY orgid ) segments_cnt ON segments_cnt.orgid = CAST(r.organization AS INT) LEFT JOIN ( SELECT orgid, max_by(name, externalsyncdate) AS latest_segment_synced FROM \"posgresreadreplica-dev\".\"public\".organizationsegment WHERE externalid IS NOT NULL GROUP BY orgid ) segments_latest ON segments_latest.orgid = CAST(r.organization AS INT) LEFT JOIN ( SELECT organizationid, MAX(created_at) AS created_at FROM \"posgresreadreplica-dev\".\"public\".organizationkeys WHERE key = 'Klaviyo-API-Key' GROUP BY organizationid ) klaviyo_key ON klaviyo_key.organizationid = CAST(r.organization AS INT) WHERE r.organization = '{orgId}' GROUP BY r.organization;",
				dataStructure: "{ \"annualrevenue\": { \"type\": \"INT64\", \"prefix\": \"$\" }, \"revenue_data_source\": { \"type\": \"UTF8\" }, \"domain\": { \"type\": \"UTF8\" }, \"raleon_is_installed\": { \"type\": \"UTF8\" }, \"shopify_connected\": { \"type\": \"datetime\" }, \"loyalty_live_date\": { \"type\": \"datetime\" }, \"raleon_plan_name\": { \"type\": \"UTF8\" }, \"raleon_plan_status\": { \"type\": \"UTF8\" }, \"raleon_plan_paid\": { \"type\": \"UTF8\" }, \"raleon_plan_amount\": { \"type\": \"INT64\", \"prefix\": \"$\" }, \"campaign_chats\": { \"type\": \"INT64\" }, \"plan_chats\": { \"type\": \"INT64\" }, \"segments_synced\": { \"type\": \"INT64\" }, \"latest_segment_synced\": { \"type\": \"UTF8\" }, \"klaviyo_connected\": { \"type\": \"datetime\" } }",
				defaultRunFrequency: "day",
				priority: 2533,
				featureId: "integrations",
				isDefault: true,
				includeInPrompt: false,
				description: "Syncs key business metrics and platform status with Hubspot for customer relationship management."
			},
			{
				name: "revenue",
				query: "WITH aggregated_refunds AS ( SELECT order_id, COALESCE(SUM(refund_amount), 0) AS total_refund_amount FROM \"{database}\".\"order_refunds\" WHERE organization = '{orgId}' GROUP BY order_id ), aggregated_orders AS ( SELECT id AS order_id, MAX(total_price) AS total_price FROM \"{database}\".\"filtered_orders\" WHERE organization = '{orgId}' AND created_at >= date_add('year', -1, current_timestamp) GROUP BY id ) SELECT SUM(o.total_price) AS total_revenue, COALESCE(SUM(r.total_refund_amount), 0) AS total_refunded FROM aggregated_orders o LEFT JOIN aggregated_refunds r ON o.order_id = r.order_id;",
				dataStructure: "{\"total_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Total Revenue\", \"prefix\": \"$\"}, \"total_refunded\": {\"type\": \"DOUBLE\", \"label\": \"Total Refunded\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 250,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks total revenue and refund amounts across all orders to measure overall business performance."
			},
			{
				name: "member_attributed_revenue",
				query: "WITH redeemers AS ( SELECT DISTINCT customer FROM \"{database}\".filtered_orders WHERE ( id IN ( SELECT order_id FROM \"{database}\".order_discounts WHERE organization = '{orgId}' AND is_non_raleon_discount = false) ) AND organization = '{orgId}'), weekly_segments AS ( SELECT customer, segment, date_trunc('week', rundate) as week FROM \"{database}\".rfm_segments WHERE organization = '{orgId}' AND rundate > TIMESTAMP '{activationDate}' AND DATE(rundate) < CURRENT_DATE), excluded_customers_org AS ( SELECT customerid FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ), unique_orders AS ( SELECT DISTINCT customer, id, total_price, member, created_at, date_trunc('week', created_at) as week FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND created_at > TIMESTAMP '{activationDate}' AND DATE(created_at) < CURRENT_DATE), order_totals AS ( SELECT uo.customer, SUM(uo.total_price) total_value, COUNT(uo.id) total_orders, max_by(uo.member, uo.created_at) member, uo.week, COALESCE(ws.segment, 'Very Loyal') as segment FROM unique_orders uo LEFT JOIN weekly_segments ws ON uo.customer = ws.customer AND uo.week = ws.week GROUP BY uo.customer, uo.week, COALESCE(ws.segment, 'Very Loyal') ), classified_customers AS ( SELECT ot.customer, ot.total_value, ot.total_orders, ot.week, ot.segment, CASE WHEN r.customer IS NOT NULL THEN 'Redeemer ' || ot.segment WHEN ot.member = true THEN 'Member ' || ot.segment ELSE 'Non-Member ' || ot.segment END AS customer_type FROM order_totals ot LEFT JOIN redeemers r ON ot.customer = r.customer WHERE ot.customer NOT IN ( SELECT customerid FROM excluded_customers_org ) ), ltv_summary AS ( SELECT customer_type, week, AVG(total_value) avg_ltv, SUM(total_orders) total_orders, SUM(total_value) total_spend FROM classified_customers GROUP BY customer_type, week ), non_member_avg AS ( SELECT week, substr( customer_type, position(' ' IN customer_type) + 1 ) AS segment, AVG(avg_ltv) as non_member_avg_ltv FROM ltv_summary WHERE customer_type LIKE 'Non-Member%' GROUP BY week, substr( customer_type, position(' ' IN customer_type) + 1 ) ), weekly_attributed_revenue AS ( SELECT ls.week, ls.customer_type, ls.avg_ltv, ls.total_orders, ls.total_spend, CASE WHEN ls.customer_type NOT LIKE 'Non-Member%' THEN GREATEST( (ls.avg_ltv - COALESCE(nm.non_member_avg_ltv, 0)), 0 ) * ls.total_orders ELSE 0 END attributed_revenue FROM ltv_summary ls LEFT JOIN non_member_avg nm ON ls.week = nm.week AND substr( ls.customer_type, position(' ' IN ls.customer_type) + 1 ) = nm.segment ), all_weeks AS ( SELECT DISTINCT week FROM weekly_attributed_revenue ), all_customer_types AS ( SELECT DISTINCT customer_type FROM weekly_attributed_revenue ), all_combinations AS ( SELECT week, customer_type FROM all_weeks CROSS JOIN all_customer_types ), full_weekly_attributed_revenue AS ( SELECT ac.week, ac.customer_type, COALESCE(war.avg_ltv, 0) as avg_ltv, COALESCE(war.total_orders, 0) as total_orders, COALESCE(war.total_spend, 0) as total_spend, COALESCE(war.attributed_revenue, 0) as attributed_revenue FROM all_combinations ac LEFT JOIN weekly_attributed_revenue war ON ac.week = war.week AND ac.customer_type = war.customer_type ), cumulative_revenue AS ( SELECT week, customer_type, attributed_revenue, sum(attributed_revenue) OVER ( PARTITION BY customer_type ORDER BY week ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) as cumulative_attributed_revenue FROM full_weekly_attributed_revenue ), major_group_totals AS ( SELECT week, CASE WHEN customer_type LIKE 'Redeemer%' THEN 'Redeemer' WHEN customer_type LIKE 'Member%' THEN 'Member' WHEN customer_type LIKE 'Non-Member%' THEN 'Non-Member' END AS major_group, SUM(total_spend) AS group_total_spend, SUM(total_orders) AS group_total_orders, SUM(cumulative_attributed_revenue) AS group_cumulative_attributed_revenue FROM full_weekly_attributed_revenue JOIN cumulative_revenue USING (week, customer_type) GROUP BY week, CASE WHEN customer_type LIKE 'Redeemer%' THEN 'Redeemer' WHEN customer_type LIKE 'Member%' THEN 'Member' WHEN customer_type LIKE 'Non-Member%' THEN 'Non-Member' END ) SELECT week as weekdate, SUM(group_cumulative_attributed_revenue) AS member_attributed_revenue, SUM(group_cumulative_attributed_revenue) - LAG(SUM(group_cumulative_attributed_revenue)) OVER (ORDER BY week) AS revenue_difference FROM major_group_totals GROUP BY week ORDER BY week",
				variables: "{\"groupByField\":\"weekdate\"}",
				dataStructure: "{\"weekdate\": { \"type\": \"datetime\" }, \"member_attributed_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"revenue_difference\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }}",
				defaultRunFrequency: "week",
				priority: 1233,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates revenue directly attributable to membership program, comparing member vs non-member purchase behavior."
			},
			{
				name: "redeemer_attribution",
				query: "WITH redeemers AS ( SELECT DISTINCT customer FROM \"{database}\".filtered_orders WHERE id IN ( SELECT order_id FROM \"{database}\".order_discounts WHERE organization = '{orgId}' AND is_non_raleon_discount = false ) AND organization = '{orgId}' ), latest_segments AS ( SELECT customer, max_by(segment, rundate) segment FROM \"{database}\".rfm_segments WHERE organization = '{orgId}' GROUP BY customer ), excluded_customers_org AS ( SELECT customerid FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ), unique_orders AS ( SELECT DISTINCT customer, id, total_price, member, created_at FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND created_at > timestamp '{activationDate}' AND total_price > 0 ), order_totals AS ( SELECT customer, AVG(total_price) total_value, COUNT(id) total_orders, max_by(member, created_at) member FROM unique_orders GROUP BY customer ), classified_customers AS ( SELECT ot.customer, ot.total_value, ot.total_orders, CASE WHEN r.customer IS NOT NULL THEN 'Redeemer' WHEN member = true THEN 'Member' ELSE 'Non-Member' END customer_type FROM order_totals ot LEFT JOIN redeemers r ON ot.customer = r.customer WHERE ot.customer NOT IN ( SELECT customerid FROM excluded_customers_org ) ), ltv_summary AS ( SELECT customer_type, AVG(total_value) avg_ltv, SUM(total_orders) total_orders FROM classified_customers GROUP BY customer_type ), non_member_avg AS ( SELECT AVG(total_value) avg_ltv FROM classified_customers WHERE customer_type = 'Non-Member' ) SELECT avg_ltv, total_orders, CASE WHEN customer_type = 'Redeemer' THEN ( ( avg_ltv - ( SELECT avg_ltv FROM non_member_avg ) ) * total_orders ) ELSE 0 END attributed_revenue FROM ltv_summary WHERE customer_type = 'Redeemer';",
				dataStructure: "{\"avg_ltv\": {\"type\": \"DOUBLE\", \"label\": \"Average LTV\", \"prefix\": \"\"}, \"total_orders\": {\"type\": \"INT64\", \"label\": \"Total Orders\", \"prefix\": \"\"}, \"attributed_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Attributed Revenue\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 550,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes revenue impact of reward redemptions by comparing redeemer vs non-redeemer purchase behavior."
			},
			{
				name: "ways_to_earn",
				query: "SELECT le.name, count(le.name) AS earn_count FROM public.organization o JOIN public.loyaltyProgram lp ON o.id = lp.orgId AND lp.active = true JOIN public.raleonuseridentity rui ON o.id = rui.orgId JOIN public.raleonuserearnlog el ON el.raleonuserid = rui.raleonuserid JOIN public.loyaltyearn le ON el.loyaltyearnid = le.id JOIN public.earncondition e on le.id = e.loyaltyearnid WHERE o.id = {orgId} and e.type != 'dollar-spent' GROUP BY le.name ORDER BY earn_count DESC LIMIT 10;",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"name\": {\"type\": \"UTF8\", \"label\": \"Coupon Name\", \"prefix\": \"\"},\"earn_count\": {\"type\": \"INT64\", \"label\": \"Earned Count\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 850,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks usage of different point-earning activities to measure program engagement across earning channels."
			},
			{
				name: "coupons_redeemed",
				query: "SELECT ic.name, count(ic.name) as used_count FROM public.organization o JOIN public.loyaltyProgram lp ON o.id = lp.orgId AND lp.active = true JOIN public.raleonuseridentity rui ON o.id = rui.orgId JOIN public.inventorycoupon ic ON ic.raleonuserid = rui.raleonuserid WHERE ic.used = true and o.id={orgId} group by ic.name ORDER BY used_count DESC LIMIT 10",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"name\": {\"type\": \"UTF8\", \"label\": \"Coupon Name\", \"prefix\": \"\"},\"used_count\": {\"type\": \"INT64\", \"label\": \"Coupons Used\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 750,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Monitors coupon redemption patterns to measure promotion effectiveness and member engagement."
			},
			{
				name: "segment_top_3",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND segment != 'New Users' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), order_ranks AS ( SELECT id, customer, row_number() OVER ( PARTITION BY customer ORDER BY created_at ) as order_rank FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' GROUP BY id, customer, created_at ), item_aggregation AS ( SELECT cs.segment, ork.order_rank, o.item_name, sum(o.item_quantity) as total_quantity, sum(o.item_price * o.item_quantity) as total_sales FROM \"{database}\".filtered_orders o JOIN order_ranks ork ON o.id = ork.id JOIN customer_segments cs ON o.customer = cs.customer WHERE  organization = '{orgId}' AND ork.order_rank IN (1, 2, 3) AND cs.segment IN ('Very Loyal', 'Not Loyal', 'Growth') GROUP BY cs.segment, ork.order_rank, o.item_name ), ranked_items AS ( SELECT segment, order_rank, item_name, total_quantity, total_sales, row_number() OVER ( PARTITION BY segment, order_rank ORDER BY total_quantity DESC ) as rank FROM item_aggregation ) SELECT segment, order_rank, item_name, total_quantity, total_sales FROM ranked_items WHERE rank <= 3 ORDER BY segment, order_rank, rank;",
				dataStructure: "{ \"segment\": { \"type\": \"UTF8\" }, \"order_rank\": { \"type\": \"INT64\" }, \"item_name\": { \"type\": \"UTF8\" }, \"total_quantity\": { \"type\": \"INT64\" }, \"total_sales\": { \"type\": \"DOUBLE\" } }",
				defaultRunFrequency: "day",
				priority: 650,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Identifies top 3 products purchased by customers in each segment to tailor marketing strategies and product offerings."
			},
			{
				name: "klaviyo_referral",
				type: "klaviyo-referral",
				query: "SELECT \"raleonuseridentity\".identityvalue, \"raleonuseridentity\".referralcode FROM \"public\".\"raleonuseridentity\" WHERE \"raleonuseridentity\".orgid = {orgId} AND ( createddate BETWEEN timestamp '{lastRunDate}' AND current_timestamp OR createddate is NULL ) AND \"raleonuseridentity\".referralcode IS NOT NULL;",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"identityvalue\": { \"type\": \"UTF8\"},\"referralcode\": { \"type\": \"UTF8\"}}",
				defaultRunFrequency: "day",
				priority: 2150,
				featureId: "integrations",
				isDefault: true,
				includeInPrompt: false,
				description: "Syncs referral codes with Klaviyo for targeted marketing campaigns and customer engagement."
			},
			{
				name: "excluded_customers",
				type: "excluded-customers",
				defaultRunFrequency: "day",
				priority: 100,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Identifies customers excluded from loyalty programs to ensure accurate reporting and analysis."
			},
			{
				name: "update_metafield",
				type: "update-metafield",
				query: "SELECT \"raleonuseridentity\".identityvalue as customer, \"loyaltycurrencybalance\".balance FROM \"public\".\"loyaltycurrencybalance\" left join \"public\".\"raleonuseridentity\" ON \"loyaltycurrencybalance\".raleonuserid = \"raleonuseridentity\".raleonuserid WHERE \"raleonuseridentity\".orgid = {orgId} AND (updateddate BETWEEN timestamp '{lastRunDate}' AND current_timestamp OR updateddate is NULL);",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"customer\": { \"type\": \"UTF8\"},\"balance\": { \"type\": \"UTF8\"}}",
				defaultRunFrequency: "day",
				priority: 2400,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Updates customer metafields with loyalty currency balance information for personalized marketing."
			},
			{
				name: "referral_revenue",
				query: "SELECT SUM(subquery.total_price) AS referral_revenue FROM ( SELECT DISTINCT id, total_price FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ) AS subquery JOIN \"{database}\".order_referrals ON subquery.id = order_referrals.order_id WHERE order_referrals.organization = '{orgId}';",
				dataStructure: "{ \"referral_revenue\": { \"type\": \"DOUBLE\", \"label\": \"Revenue from Referrals\", \"prefix\": \"$\" } }",
				defaultRunFrequency: "day",
				priority: 2100,
				featureId: "referrals",
				isDefault: true,
				includeInPrompt: false,
				description: "Sums the total revenue from completed orders that were attributed to referrals. Quantifies the financial return generated through the referral program."
			},
			{
				name: "referral_conversion_percentage",
				query: "WITH CompletedReferrals AS ( SELECT COUNT(*) AS CompletedCount FROM public.raleonuseridentity WHERE orgid = {orgId} AND signupreferrer IS NOT NULL AND referralcomplete = true ), ReferredButNotCompleted AS ( SELECT COUNT(*) AS NotCompletedCount FROM public.raleonuseridentity WHERE orgid = {orgId} AND signupreferrer IS NOT NULL AND (referralcomplete = false OR referralcomplete IS NULL) ) SELECT CASE WHEN ReferredButNotCompleted.NotCompletedCount = 0 THEN 0 ELSE ( CAST(CompletedReferrals.CompletedCount AS DOUBLE PRECISION) \/ ReferredButNotCompleted.NotCompletedCount ) * 100 END AS ConversionPercentage FROM CompletedReferrals, ReferredButNotCompleted",
				catalog: "posgresreadreplicav3",
				dataStructure: "{ \"referral_conversion_percentage\": { \"type\": \"DOUBLE\", \"label\": \"Referral Conversion Rate\", \"suffix\": \"%\" } }",
				defaultRunFrequency: "day",
				priority: 2000,
				featureId: "referrals",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates the referral conversion rate by comparing completed referrals to those initiated but not completed. Helps evaluate the effectiveness of the referral funnel."
			},
			{
				name: "referrals_not_converted",
				query: "SELECT COUNT(*) AS NotCompletedCount FROM public.raleonuseridentity WHERE orgid = {orgId} AND signupreferrer IS NOT NULL AND (referralcomplete = false OR referralcomplete IS NULL)",
				catalog: "posgresreadreplicav3",
				dataStructure: "{ \"referral_not_complete_count\": { \"type\": \"INT64\", \"label\": \"Referrals Not Completed Count\", \"prefix\": \"\" } }",
				defaultRunFrequency: "day",
				priority: 2300,
				featureId: "referrals",
				isDefault: true,
				includeInPrompt: false,
				description: "Counts users who signed up via a referral link but haven't completed the referral requirements. Highlights unrealized referral potential."
			},
			{
				name: "referral_conversions",
				query: "SELECT COUNT(*) AS CompletedCount FROM public.raleonuseridentity WHERE orgid = {orgId} AND signupreferrer IS NOT NULL AND referralcomplete = true",
				catalog: "posgresreadreplicav3",
				dataStructure: "{ \"referral_complete_count\": { \"type\": \"INT64\", \"label\": \"Referral Conversion Count\", \"prefix\": \"\" } }",
				defaultRunFrequency: "day",
				priority: 2200,
				featureId: "referrals",
				isDefault: true,
				includeInPrompt: false,
				description: "Counts how many referrals have been successfully completed. Used to track the volume of confirmed referral-based customer acquisitions."
			},
			{
				name: "customer_io",
				type: "customer-io",
				query: "SELECT ( SELECT max_by(member_revenue, rundate) FROM \"{database}\".\"loyalty_revenue\" WHERE organization = '{orgId}' ) AS member_revenue, ( SELECT max_by(totalpoints, rundate) FROM \"{database}\".\"loyalty_points_balance\" WHERE organization = '{orgId}' ) AS total_points, ( SELECT max_by(adoption_rate, rundate) FROM \"{database}\".\"loyalty_adoption_rate\" WHERE organization = '{orgId}' ) AS adoption_rate, ( SELECT max_by(active_members, rundate) FROM \"{database}\".\"active_members\" WHERE organization = '{orgId}' ) AS active_members, ( SELECT cast(max_by(total_revenue, rundate) as bigint) FROM \"{database}\".\"revenue\" WHERE organization = '{orgId}' ) AS total_revenue, ( SELECT EXISTS ( SELECT 1 FROM \"posgresreadreplicav3\".\"public\".organization WHERE createddate < current_timestamp - INTERVAL '1' DAY AND historicaldatacomplete is NULL and id = {orgId} ) AS historical_data_error ) AS historical_data_error, ( SELECT EXISTS ( SELECT 1 FROM \"posgresreadreplicav3\".\"public\".organizationsettings WHERE key = 'klaviyo_error' AND organizationid = {orgId} ) AS has_klaviyo_error ) AS klaviyo_error;",
				dataStructure: "{\"member_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"total_points\": { \"type\": \"DOUBLE\" }, \"adoption_rate\": { \"type\": \"DOUBLE\" }, \"active_members\": { \"type\": \"INT64\" }, \"total_revenue\": { \"type\": \"DOUBLE\", \"prefix\": \"$\" }, \"historical_data_error\": { \"type\": \"UTF8\" }, \"klaviyo_error\": { \"type\": \"UTF8\" }}",
				defaultRunFrequency: "month",
				priority: 3000,
				featureId: "integrations",
				isDefault: true,
				includeInPrompt: false,
				description: "Syncs key business metrics and platform status with Customer.io for customer relationship management."
			},
			{
				name: "segment_purchase_burndown",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND segment IN ('Growth', 'Very Loyal') AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), total_customers_per_segment AS ( SELECT segment, COUNT(*) AS total_customers FROM customer_segments GROUP BY segment ), orders_by_customer AS ( SELECT cs.customer, cs.segment, COUNT(DISTINCT o.id) AS order_count FROM \"{database}\".\"filtered_orders\" o JOIN customer_segments cs ON o.customer = cs.customer WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) GROUP BY cs.customer, cs.segment ), order_counts AS ( SELECT DISTINCT order_count FROM orders_by_customer ), repeat_purchase_counts AS ( SELECT oc.order_count, cs.segment, COUNT(cs.customer) AS num_customers FROM order_counts oc CROSS JOIN customer_segments cs LEFT JOIN orders_by_customer obc ON cs.customer = obc.customer AND oc.order_count <= obc.order_count WHERE obc.customer IS NOT NULL GROUP BY oc.order_count, cs.segment ) SELECT rpc.order_count AS number_of_purchases, MAX( CASE WHEN rpc.segment = 'Growth' THEN rpc.num_customers ELSE 0 END ) AS customers_growth, MAX( CASE WHEN rpc.segment = 'Very Loyal' THEN rpc.num_customers ELSE 0 END ) AS customers_very_loyal, ROUND( MAX( CASE WHEN rpc.segment = 'Growth' THEN rpc.num_customers ELSE 0 END ) * 100.0 \/ MAX( CASE WHEN tcs.segment = 'Growth' THEN tcs.total_customers ELSE 0 END ), 2 ) AS growth_percentage, ROUND( MAX( CASE WHEN rpc.segment = 'Very Loyal' THEN rpc.num_customers ELSE 0 END ) * 100.0 \/ MAX( CASE WHEN tcs.segment = 'Very Loyal' THEN tcs.total_customers ELSE 0 END ), 2 ) AS very_loyal_percentage FROM repeat_purchase_counts rpc CROSS JOIN total_customers_per_segment tcs GROUP BY rpc.order_count ORDER BY number_of_purchases",
				dataStructure: "{\"number_of_purchases\": { \"type\": \"INT64\", \"label\": \"Number of Purchases\", \"prefix\": \"\" },\"customers_growth\": { \"type\": \"INT64\", \"label\": \"Growth Customers\", \"prefix\": \"\" },\"customers_very_loyal\": { \"type\": \"INT64\", \"label\": \"Loyal Customers\", \"prefix\": \"\" },\"growth_percentage\": { \"type\": \"DOUBLE\", \"label\": \"Growth Percentage\", \"suffix\": \"%\" },\"very_loyal_percentage\": { \"type\": \"DOUBLE\", \"label\": \"Loyal Percentage\", \"suffix\": \"%\" }}",
				defaultRunFrequency: "day",
				priority: 1200,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes how many customers in the Growth and Very Loyal segments have made a given number of purchases over the past year. Used to understand purchase frequency distributions by segment."
			},
			{
				name: "segment_to_very_loyal",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), distinct_orders AS ( SELECT DISTINCT o.customer, o.id AS order_id, o.total_price AS order_total FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), segment_count_growth AS ( SELECT COUNT(DISTINCT customer) AS segment_count FROM customer_segments WHERE segment = 'Growth' ), cltv_very_loyal AS ( SELECT ROUND( SUM(do.order_total) \/ COUNT(DISTINCT do.customer), 2 ) AS cltv FROM distinct_orders do JOIN customer_segments cs ON do.customer = cs.customer WHERE cs.segment = 'Very Loyal' ), cltv_growth AS ( SELECT ROUND( SUM(do.order_total) \/ COUNT(DISTINCT do.customer), 2 ) AS cltv FROM distinct_orders do JOIN customer_segments cs ON do.customer = cs.customer WHERE cs.segment = 'Growth' ) SELECT ROUND( ROUND(0.01 * scg.segment_count, 2) * (cltv_vl.cltv - cltv_g.cltv), 2 ) AS potential_revenue_opportunity, ROUND(0.01 * scg.segment_count, 2) AS potential_customer_count FROM segment_count_growth scg, cltv_very_loyal cltv_vl, cltv_growth cltv_g;",
				dataStructure: "{\"potential_revenue_opportunity\": { \"type\": \"DOUBLE\", \"label\": \"Potential Revenue Opportunity\", \"prefix\": \"$\" },\"potential_customer_count\": { \"type\": \"DOUBLE\", \"label\": \"Potential Customer Count\", \"prefix\": \"\" }}",
				defaultRunFrequency: "day",
				priority: 1800,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Estimates the incremental revenue from converting 1% of Growth segment customers to Very Loyal based on their respective CLTVs. Guides prioritization of loyalty upgrade initiatives."
			},
			{
				name: "segment_percentage_total_spend",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), distinct_orders AS ( SELECT DISTINCT o.customer, o.id AS order_id, o.total_price AS order_total FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), aggregated_order_totals AS ( SELECT cs.segment, SUM(aot.order_total) AS segment_total_spend FROM distinct_orders aot JOIN customer_segments cs ON aot.customer = cs.customer GROUP BY cs.segment ), total_spend AS ( SELECT SUM(order_total) AS total_order_spend FROM distinct_orders ) SELECT ROUND( SUM( CASE WHEN segment = 'New Users' THEN segment_total_spend ELSE 0 END ) * 100.0 \/ ts.total_order_spend, 2 ) AS percentage_spend_new_users, ROUND( SUM( CASE WHEN segment = 'Not Loyal' THEN segment_total_spend ELSE 0 END ) * 100.0 \/ ts.total_order_spend, 2 ) AS percentage_spend_not_loyal, ROUND( SUM( CASE WHEN segment = 'Growth' THEN segment_total_spend ELSE 0 END ) * 100.0 \/ ts.total_order_spend, 2 ) AS percentage_spend_growth, ROUND( SUM( CASE WHEN segment = 'Very Loyal' THEN segment_total_spend ELSE 0 END ) * 100.0 \/ ts.total_order_spend, 2 ) AS percentage_spend_very_loyal FROM aggregated_order_totals, total_spend ts GROUP BY ts.total_order_spend;",
				dataStructure: "{\"percentage_spend_new_users\": { \"type\": \"DOUBLE\", \"label\": \"New Customers\", \"prefix\": \"$\" },\"percentage_spend_not_loyal\": { \"type\": \"DOUBLE\", \"label\": \"Not Loyal\", \"prefix\": \"$\" },\"percentage_spend_growth\": { \"type\": \"DOUBLE\", \"label\": \"Growth\", \"prefix\": \"$\" },\"percentage_spend_very_loyal\": { \"type\": \"DOUBLE\", \"label\": \"Very Loyal\", \"prefix\": \"$\" }}",
				defaultRunFrequency: "day",
				priority: 1700,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Shows what percentage of total spend each segment (New Users, Not Loyal, Growth, Very Loyal) contributes. Useful for identifying which customer segments drive revenue."
			},
			{
				name: "segment_percentage_total_customers",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), total_customers AS ( SELECT COUNT(DISTINCT customer) AS total_count FROM customer_segments ) SELECT ROUND( SUM( CASE WHEN segment = 'New Users' THEN 1 ELSE 0 END ) * 100.0 \/ tc.total_count, 2 ) AS New_Users, ROUND( SUM( CASE WHEN segment = 'Not Loyal' THEN 1 ELSE 0 END ) * 100.0 \/ tc.total_count, 2 ) AS Not_Loyal, ROUND( SUM( CASE WHEN segment = 'Growth' THEN 1 ELSE 0 END ) * 100.0 \/ tc.total_count, 2 ) AS Growth, ROUND( SUM( CASE WHEN segment = 'Very Loyal' THEN 1 ELSE 0 END ) * 100.0 \/ tc.total_count, 2 ) AS Very_Loyal FROM customer_segments, total_customers tc GROUP BY tc.total_count;",
				dataStructure: "{\"New_Users\": { \"type\": \"DOUBLE\", \"label\": \"New Customers\", \"prefix\": \"$\" },\"Not_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Not Loyal\", \"prefix\": \"$\" },\"Growth\": { \"type\": \"DOUBLE\", \"label\": \"Growth\", \"prefix\": \"$\" },\"Very_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Very Loyal\", \"prefix\": \"$\" }}",
				defaultRunFrequency: "day",
				priority: 1600,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Breaks down the customer base by the percentage in each loyalty segment. Used for understanding the distribution of customer loyalty levels."
			},
			{
				name: "segment_cltv",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), distinct_orders AS ( SELECT DISTINCT o.customer, o.id AS order_id, o.total_price AS order_total FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), aggregated_order_totals AS ( SELECT cs.segment, aot.customer, SUM(aot.order_total) AS total_order_value FROM distinct_orders aot JOIN customer_segments cs ON aot.customer = cs.customer GROUP BY cs.segment, aot.customer ) SELECT CASE WHEN COUNT( DISTINCT CASE WHEN segment = 'New Users' THEN customer END ) = 0 THEN 0 ELSE ROUND( SUM( CASE WHEN segment = 'New Users' THEN total_order_value ELSE 0 END ) \/ COUNT( DISTINCT CASE WHEN segment = 'New Users' THEN customer END ), 2 ) END AS New_Users, CASE WHEN COUNT( DISTINCT CASE WHEN segment = 'Not Loyal' THEN customer END ) = 0 THEN 0 ELSE ROUND( SUM( CASE WHEN segment = 'Not Loyal' THEN total_order_value ELSE 0 END ) \/ COUNT( DISTINCT CASE WHEN segment = 'Not Loyal' THEN customer END ), 2 ) END AS Not_Loyal, CASE WHEN COUNT( DISTINCT CASE WHEN segment = 'Growth' THEN customer END ) = 0 THEN 0 ELSE ROUND( SUM( CASE WHEN segment = 'Growth' THEN total_order_value ELSE 0 END ) \/ COUNT( DISTINCT CASE WHEN segment = 'Growth' THEN customer END ), 2 ) END AS Growth, CASE WHEN COUNT( DISTINCT CASE WHEN segment = 'Very Loyal' THEN customer END ) = 0 THEN 0 ELSE ROUND( SUM( CASE WHEN segment = 'Very Loyal' THEN total_order_value ELSE 0 END ) \/ COUNT( DISTINCT CASE WHEN segment = 'Very Loyal' THEN customer END ), 2 ) END AS Very_Loyal FROM aggregated_order_totals;",
				dataStructure: "{\"New_Users\": { \"type\": \"DOUBLE\", \"label\": \"New Customers\", \"prefix\": \"$\" },\"Not_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Not Loyal\", \"prefix\": \"$\" },\"Growth\": { \"type\": \"DOUBLE\", \"label\": \"Growth\", \"prefix\": \"$\" },\"Very_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Very Loyal\", \"prefix\": \"$\" }}",
				defaultRunFrequency: "day",
				priority: 1400,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Breaks down customer lifetime value by loyalty segments (New Users, Not Loyal, Growth, Very Loyal) to compare long-term customer value across segments."
			},
			{
				name: "segment_aov",
				query: "WITH customer_segments AS ( SELECT customer, max_by(segment, rundate) AS segment FROM \"{database}\".\"rfm_segments\" WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), distinct_orders AS ( SELECT DISTINCT o.customer, o.id AS order_id, o.total_price AS order_total FROM \"{database}\".\"filtered_orders\" o WHERE o.organization = '{orgId}' AND o.created_at >= date_add('month', -12, current_date) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), aggregated_order_totals AS ( SELECT cs.segment, aot.customer, SUM(aot.order_total) AS total_order_value, COUNT(aot.order_id) AS order_count FROM distinct_orders aot JOIN customer_segments cs ON aot.customer = cs.customer GROUP BY cs.segment, aot.customer ) SELECT ROUND( SUM( CASE WHEN segment = 'New Users' THEN total_order_value ELSE 0 END ) \/ SUM( CASE WHEN segment = 'New Users' THEN order_count ELSE 0 END ), 2 ) AS New_Users, ROUND( SUM( CASE WHEN segment = 'Not Loyal' THEN total_order_value ELSE 0 END ) \/ SUM( CASE WHEN segment = 'Not Loyal' THEN order_count ELSE 0 END ), 2 ) AS Not_Loyal, ROUND( SUM( CASE WHEN segment = 'Growth' THEN total_order_value ELSE 0 END ) \/ SUM( CASE WHEN segment = 'Growth' THEN order_count ELSE 0 END ), 2 ) AS Growth, ROUND( SUM( CASE WHEN segment = 'Very Loyal' THEN total_order_value ELSE 0 END ) \/ SUM( CASE WHEN segment = 'Very Loyal' THEN order_count ELSE 0 END ), 2 ) AS Very_Loyal FROM aggregated_order_totals",
				dataStructure: "{\"New_Users\": { \"type\": \"DOUBLE\", \"label\": \"New Customers\", \"prefix\": \"$\" },\"Not_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Not Loyal\", \"prefix\": \"$\" },\"Growth\": { \"type\": \"DOUBLE\", \"label\": \"Growth\", \"prefix\": \"$\" },\"Very_Loyal\": { \"type\": \"DOUBLE\", \"label\": \"Very Loyal\", \"prefix\": \"$\" }}",
				defaultRunFrequency: "day",
				priority: 1300,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Analyzes average order value by customer loyalty segment to understand spending patterns across different customer groups."
			},
			{
				name: "campaign_roi",
				query: "WITH Alldata AS ( SELECT id, campaign_id, SUM(total_price) as campaign_revenue, SUM(discount_amount) as campaign_spend FROM \"{database}\".\"order_discounts\" left join \"{database}\".\"filtered_orders\" on order_id = id where filtered_orders.organization = '{orgId}' and order_discounts.organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) group by id, campaign_id ) Select campaign_id, SUM(campaign_revenue) as campaign_revenue, SUM(campaign_spend) as campaign_spend, SUM(campaign_revenue) - SUM(campaign_spend) \/ SUM(campaign_spend) * 100 as campaign_roi from Alldata group by campaign_id;",
				dataStructure: "{\"campaign_id\": {\"type\": \"INT64\", \"label\": \"Campaign Id\", \"prefix\": \"\"},\"campaign_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Campaign Revenue\", \"prefix\": \"$\"},\"campaign_spend\": {\"type\": \"DOUBLE\", \"label\": \"Campaign Spend\", \"prefix\": \"$\"},\"campaign_roi\": {\"type\": \"DOUBLE\", \"label\": \"Campaign ROI\", \"prefix\": \"\", \"suffix\": \"%\"}}",
				defaultRunFrequency: "day",
				priority: 600,
				featureId: "campaigns",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates return on investment for marketing campaigns by comparing campaign revenue to campaign spend, including discount amounts."
			},
			{
				name: "members_segment",
				query: "WITH DeduplicatedSegments AS ( SELECT customer, segment, ROW_NUMBER() OVER ( PARTITION BY customer ORDER BY rundate DESC ) as rn FROM \"{database}\".rfm_segments WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ) SELECT COUNT( CASE WHEN segment = 'Not Loyal' AND rn = 1 THEN 1 ELSE NULL END ) AS not_loyal_count, COUNT( CASE WHEN segment = 'New Users' AND rn = 1 THEN 1 ELSE NULL END ) AS new_users_count, COUNT( CASE WHEN segment = 'Very Loyal' AND rn = 1 THEN 1 ELSE NULL END ) AS very_loyal_count, COUNT( CASE WHEN segment = 'Growth' AND rn = 1 THEN 1 ELSE NULL END ) AS some_loyalty_count FROM DeduplicatedSegments;",
				dataStructure: "{\"not_loyal\": {\"type\": \"INT64\", \"label\": \"Not Loyal\", \"prefix\": \"\"},\"new_users\": {\"type\": \"INT64\", \"label\": \"New User\", \"prefix\": \"\"},\"very_loyal\": {\"type\": \"INT64\", \"label\": \"Very Loyal\", \"prefix\": \"\"},\"some_loyalty\": {\"type\": \"INT64\", \"label\": \"Growth\", \"prefix\": \"\"}}",
				defaultRunFrequency: "day",
				priority: 1100,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Counts the number of customers currently in each loyalty segment (Not Loyal, New Users, Growth, Very Loyal), based on their most recent segment classification. Provides a snapshot of the customer base's loyalty distribution."
			},
			{
				name: "loyalty_revenue_month",
				query: "WITH distinct_orders AS ( SELECT DISTINCT id, member, total_price, created_at FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND created_at BETWEEN date_add('day', -30, current_date) AND current_timestamp AND customer NOT IN ( SELECT customerid FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), uniqueorders AS ( SELECT id, member, SUM(total_price) AS OrderTotal, DATE_TRUNC('day', created_at) AS created_day FROM distinct_orders GROUP BY id, member, DATE_TRUNC('day', created_at) ) SELECT created_day, ROUND( SUM( CASE WHEN member THEN OrderTotal ELSE 0 END ), 2 ) AS member_revenue, ROUND( SUM( CASE WHEN NOT member THEN OrderTotal ELSE 0 END ), 2 ) AS nonmember_revenue FROM uniqueorders GROUP BY created_day ORDER BY created_day;",
				variables: "{\"groupByField\":\"created_day\"}",
				dataStructure: "{\"created_day\": { \"type\": \"datetime\" }, \"member_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Member Revenue\", \"prefix\": \"$\"}, \"nonmember_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Nonmember Revenue\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 500,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks daily revenue over the past 30 days, split between members and non-members. Helps monitor loyalty program contribution to revenue on a day-by-day basis."
			},
			{
				name: "loyalty_revenue",
				query: "WITH distinct_orders AS ( SELECT DISTINCT id, member, total_price FROM \"{database}\".filtered_orders WHERE created_at >= date_add('day', -30, current_date) AND organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) ), uniqueorders AS ( SELECT id, member, SUM(total_price) AS OrderTotal FROM distinct_orders GROUP BY id, member ) SELECT ROUND( SUM( CASE WHEN member THEN OrderTotal ELSE 0 END ), 2 ) AS member_revenue, ROUND( SUM( CASE WHEN NOT member THEN OrderTotal ELSE 0 END ), 2 ) AS nonmember_revenue FROM uniqueorders;",
				dataStructure: "{\"member_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Member Revenue\", \"prefix\": \"$\"}, \"nonmember_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Nonmember Revenue\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 400,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Aggregates total revenue from members and non-members over the past 30 days. Used to measure the overall impact of the loyalty program on revenue."
			},
			{
				name: "active_members_month",
				query: "WITH AllMembers AS ( SELECT customer, MIN(created_at) AS first_transaction_date, MAX(created_at) AS last_transaction_date FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND member = true AND customer NOT IN ( SELECT customerid FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ), DateRange AS ( SELECT date_add('day', - seq, current_date) AS date FROM ( SELECT sequence(0, 29) AS seq_array ) AS t CROSS JOIN UNNEST(seq_array) AS t(seq) ) SELECT date_range.date, COUNT(DISTINCT active_members.customer) AS active_members, COUNT(DISTINCT new_members.customer) AS new_members FROM DateRange date_range LEFT JOIN AllMembers active_members ON date_trunc('day', active_members.last_transaction_date) = date_range.date LEFT JOIN AllMembers new_members ON date_trunc('day', new_members.first_transaction_date) = date_range.date GROUP BY date_range.date ORDER BY date_range.date;",
				variables: "{\"groupByField\":\"date\"}",
				dataStructure: "{\"date\": { \"type\": \"datetime\" }, \"active_members\": { \"type\": \"INT64\", \"label\": \"Active Members\", \"prefix\": \"\" },\"new_members\": { \"type\": \"INT64\", \"label\": \"New Members\", \"prefix\": \"\"  }}",
				defaultRunFrequency: "day",
				priority: 1000,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Counts daily active and new members based on transaction activity in the past 30 days. Useful for identifying engagement trends and member acquisition rates."
			},
			{
				name: "loyalty_adoption_rate",
				query: "WITH MemberCustomers AS ( SELECT DISTINCT customer FROM \"{database}\".filtered_orders WHERE member = true AND organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) AND created_at >= timestamp '{activationDate}' ), TotalCustomers AS ( SELECT DISTINCT customer FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) AND created_at >= timestamp '{activationDate}' ) SELECT ( SELECT COUNT(*) FROM MemberCustomers ) AS total_members, ( SELECT COUNT(*) FROM TotalCustomers ) AS total_customers, ( SELECT COUNT(*) FROM MemberCustomers ) * 1.0 \/ ( SELECT COUNT(*) FROM TotalCustomers ) * 100 AS adoption_rate",
				dataStructure: "{\"total_members\": { \"type\": \"INT64\", \"label\": \"Total Members\", \"prefix\": \"\" },\"total_customers\": { \"type\": \"INT64\", \"label\": \"Total Customers\", \"prefix\": \"\" },\"adoption_rate\": { \"type\": \"DOUBLE\", \"label\": \"Adoption Rate\", \"suffix\": \"%\" }}",
				defaultRunFrequency: "day",
				priority: 800,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Calculates the percentage of customers who are members, based on purchases since the loyalty program activation date. Measures adoption and penetration of the loyalty program."
			},
			{
				name: "roi",
				query: "WITH RevenueData AS ( SELECT member, SUM(total_price) AS total_revenue, COUNT(DISTINCT id) AS total_orders, MAX(loyalty_spend) AS total_loyalty_spend FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND created_at >= date_add('day', -90, CURRENT_DATE) AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY member ), TotalLoyaltyMembers AS ( SELECT COUNT(DISTINCT customer) AS total_loyalty_members FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND member = true AND created_at >= date_add('day', -90, CURRENT_DATE) ), AggregatedData AS ( SELECT SUM( CASE WHEN member = true THEN total_revenue END ) \/ NULLIF( SUM( CASE WHEN member = true THEN total_orders END ), 0 ) AS ARPM, SUM( CASE WHEN member = true THEN total_loyalty_spend END ) AS total_loyalty_spend, SUM( CASE WHEN member = false THEN total_revenue END ) \/ NULLIF( SUM( CASE WHEN member = false THEN total_orders END ), 0 ) AS ARPNM FROM RevenueData ) SELECT COALESCE( (ARPM - COALESCE(ARPNM, 0)) \/ (NULLIF(total_loyalty_spend, 0)\/NULLIF(TLM.total_loyalty_members, 0)) * 100, 0 ) AS roi FROM AggregatedData,TotalLoyaltyMembers AS TLM",
				dataStructure: "{\"roi\": {\"type\": \"DOUBLE\", \"label\": \"ROLS\", \"suffix\": \"%\"}}",
				defaultRunFrequency: "day",
				priority: 2700,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Estimates Return on Loyalty Spend (ROLS) by comparing the difference in revenue per order between members and non-members against loyalty program cost per member. Indicates cost-efficiency of the loyalty program."
			},
			{
				name: "rfm_segments",
				type: "loyalty-segment",
				defaultRunFrequency: "day",
				priority: 200,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Segments customers based on Recency, Frequency, and Monetary value of purchases for targeted marketing."
			},
			{
				name: "loyalty_points_balance",
				query: "WITH currency AS (SELECT id, conversiontousd FROM public.loyaltycurrency WHERE loyaltyprogramid IN (SELECT id FROM public.loyaltyprogram WHERE orgid={orgId})) SELECT SUM(CAST(balance as DECIMAL(20,2)))\/conversiontousd as totalusd, SUM(balance) as totalpoints FROM \"public\".\"loyaltycurrencybalance\" INNER JOIN currency ON loyaltycurrencyid=currency.id WHERE balance >0 group by conversiontousd;",
				catalog: "posgresreadreplicav3",
				dataStructure: "{\"totalusd\": { \"type\": \"DOUBLE\", \"label\": \"Total USD\", \"prefix\": \"$\"},\"totalpoints\": { \"type\": \"INT64\", \"label\": \"Total Points\", \"prefix\": \"\" }}",
				defaultRunFrequency: "day",
				priority: 2600,
				featureId: "performance-dashboard",
				isDefault: true,
				includeInPrompt: false,
				description: "Tracks total loyalty points and their USD value across all members to monitor program liability and engagement."
			},
			{
				name: "segment_loyalty_revenue",
				query: "WITH AggregatedOrders AS ( SELECT o.id, o.customer, o.total_price FROM \"{database}\".filtered_orders o WHERE o.organization = '{orgId}' AND o.customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY o.id, o.customer, o.total_price ), MaxRunDate AS ( SELECT customer, MAX(rundate) AS MaxDate FROM \"{database}\".rfm_segments WHERE organization = '{orgId}' AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY customer ) SELECT SUM(CASE WHEN r.segment = 'Not Loyal' THEN ao.total_price ELSE 0 END) AS not_loyal_revenue, SUM(CASE WHEN r.segment = 'New Users' THEN ao.total_price ELSE 0 END) AS new_users_revenue, SUM(CASE WHEN r.segment = 'Very Loyal' THEN ao.total_price ELSE 0 END) AS very_loyal_revenue, SUM(CASE WHEN r.segment = 'Growth' THEN ao.total_price ELSE 0 END) AS some_loyalty_revenue FROM AggregatedOrders ao JOIN MaxRunDate mrd ON ao.customer = mrd.customer JOIN \"{database}\".rfm_segments r ON ao.customer = r.customer AND r.rundate = mrd.MaxDate WHERE r.organization = '{orgId}';",
				dataStructure: "{\"not_loyal_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Not Loyal Revenue\", \"prefix\": \"$\"},\"new_users_revenue\": {\"type\": \"DOUBLE\", \"label\": \"New User Revenue\", \"prefix\": \"$\"},\"very_loyal_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Very Loyal Revenue\", \"prefix\": \"$\"},\"some_loyalty_revenue\": {\"type\": \"DOUBLE\", \"label\": \"Growth Revenue\", \"prefix\": \"$\"}}",
				defaultRunFrequency: "day",
				priority: 700,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Breaks down revenue by customer loyalty segments to understand value contribution of each segment."
			},
			{
				name: "member_sales",
				query: "WITH filteredorders AS ( SELECT id, member, total_price FROM \"{database}\".filtered_orders WHERE organization = '{orgId}' AND created_at >= current_date - interval '30' day AND customer NOT IN ( SELECT customerId FROM \"{database}\".excluded_customers WHERE organization = '{orgId}' ) GROUP BY id, member, total_price ), totals AS ( SELECT SUM(total_price) AS total_sales FROM filteredorders ) SELECT SUM( CASE WHEN member = FALSE THEN 1 ELSE 0 END ) AS non_member_orders, SUM( CASE WHEN member = FALSE THEN total_price ELSE 0 END ) AS non_member_sales, SUM( CASE WHEN member = TRUE THEN 1 ELSE 0 END ) AS member_orders, SUM( CASE WHEN member = TRUE THEN total_price ELSE 0 END ) AS member_sales, ( SUM( CASE WHEN member = true THEN total_price ELSE 0 END ) \/ NULLIF(SUM(total_price), 0) ) * 100.0 AS member_sales_percentage, ( SUM( CASE WHEN member = false THEN total_price ELSE 0 END ) \/ NULLIF(SUM(total_price), 0) ) * 100.0 AS non_member_sales_percentage FROM filteredorders, totals;",
				dataStructure: "{\"non_member_orders\": { \"type\": \"INT64\", \"label\": \"Non-Member Orders\", \"prefix\": \"\" },\"non_member_sales\": { \"type\": \"DOUBLE\", \"label\": \"Non-Member Sales\", \"prefix\": \"$\"  },\"member_orders\": { \"type\": \"INT64\", \"label\": \"Member Orders\", \"prefix\": \"\"  },\"member_sales\": { \"type\": \"DOUBLE\", \"label\": \"Member Sales\", \"prefix\": \"$\"  },\"member_sales_percent\": { \"type\": \"DOUBLE\", \"label\": \"Member Sales\", \"suffix\": \"%\"  },\"non_member_sales_percent\": { \"type\": \"DOUBLE\", \"label\": \"Non-Member Sales\", \"suffix\": \"%\"  }}",
				defaultRunFrequency: "day",
				priority: 1500,
				featureId: "member-insights-dashboards",
				isDefault: true,
				includeInPrompt: false,
				description: "Compares sales metrics between members and non-members including order counts and revenue percentages."
			}
		];

		const existingMetrics = await this.metricRepository.find();

		for (const metric of metricData) {
			const existingMetric = existingMetrics.find(m => m.name === metric.name);
			if (!existingMetric) {
				await this.metricRepository.create(metric);
				console.log(`Created metric: ${metric.name}`);
			} else if (existingMetrics.filter(m => m.name === metric.name).length > 1) {
				throw new Error(`Multiple metrics found with key: ${metric.name}. Manually correct`);
			} else {
				await this.metricRepository.updateById(existingMetric.id, metric);
				console.log(`Updated metric: ${metric.name}`);
			}
		}
	}

	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
