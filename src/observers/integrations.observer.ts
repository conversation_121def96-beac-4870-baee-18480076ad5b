import {
	/* inject, Application, CoreBindings, */
	lifeCycleObserver, // The decorator
	LifeCycleObserver, // The interface
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {IntegrationRepository} from '../repositories';
import {ALifeCycleObserver} from './a-lifecycle-observer';

/**
 * This class will be bound to the application as a `LifeCycleObserver` during
 * `boot`
 */
@lifeCycleObserver('Data')
export class IntegrationsObserver extends ALifeCycleObserver {
	constructor(
		@repository(IntegrationRepository) private integrationRepo: IntegrationRepository,
	) {
		super();
	}


	/**
	 * This method will be invoked when the application initializes. It will be
	 * called at most once for a given application instance.
	 */
	async init(): Promise<void> {
		// Add your logic for init
	}

	/**
	 * This method will be invoked when the application starts.
	 */
	async start(): Promise<void> {
		if (!super.shouldRun()) return;
		console.log("STARTING INTEGRATIONS OBSERVER");
		//You can switch on this for production/dev differences
		let appURL = process.env.RALEON_WEBAPP_URL || "https://dev.raleon.io";
		let judgeMeClientID = process.env.JUDGE_ME_CLIENT_ID || "65c8f8f747f858c497c991720d210e99f97c072d83e0045980fba419f3948f34"
		let shopifyURL = `https://{STORE_URL}/admin/oauth/authorize?client_id=82546e163c0515df8ec1c7df5b26fb4a&redirect_uri=https://loyalty.raleon.io/auth/callback&state=orgId:{ORGID},userId:{USERID}`;
		if(process.env.NODE_ENV != 'production') {
			console.log("This is a dev environment")
			shopifyURL = `https://{STORE_URL}/admin/oauth/authorize?client_id=01734106b750bd36db4990964a24a3ce&redirect_uri=https://loyalty-dev.raleon.io/auth/callback&state=orgId:{ORGID},userId:{USERID}`;
		}

		// Add your logic for start
		let integrations = [
			{
				id: 1, name: "JudgeMe",
				description: "Connect to your JudgeMe integration to enable Product Review Ways To Earn",
				category: "product-review",
				exchangeApiUrl: "/integration/judgeme/exchange",
				oAuthURL: `https://judge.me/oauth/authorize?client_id=${judgeMeClientID}&redirect_uri=${appURL}/integrations?name=judgeme&response_type=code&scope=read_reviews%20read_reviewers%20read_orders%20read_products`,
				imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/judgeme.png",
				docURL: "https://docs.raleon.io/docs/judgeme-reviews",
				docURLText: "JudgeMe Documentation",
				enableDisableURL: "/integration/judgeme/enable",
				showConnectButton: true,
				canBeActivated: true,
				sortOrder: 1000,
				errorMessage: "Error connecting to JudgeMe: Note that you must have a paid JudgeMe account to connect to Raleon."
			},
			{
				id: 2, name: "Klaviyo",
				description: "Klaviyo is a powerful email marketing platform, utilize Raleon loyalty segments within Klaviyo.",
				category: "",
				exchangeApiUrl: "",
				oAuthURL: ``,
				imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/klaviyo.png",
				docURL: "https://docs.raleon.io/docs/klavi",
				docURLText: "Klaviyo Documentation",
				enableDisableURL: "",
				showConnectButton: false,
				canBeActivated: false,
				sortOrder: 200,
				fieldMappings: JSON.stringify({
					identityvalue: "customer",
					referralcode: "Raleon Referral Code (Klaviyo)",
					ltvdistribution: "Raleon LTV Distribution Score",
					ltv: "Raleon LTV",
					revenue: "Raleon Revenue",
					replenishmentscore: "Raleon Replenishment Score",
					churnrisk: "Raleon Churn Risk",
					refundpropensity: "Raleon Refund Propensity Score",
					discountscore: "Raleon Discount Score",
					aov: "Raleon AOV",
					totalorders: "Raleon Total Orders",
					totalrefunds: "Raleon Total Refunds",
					replenishmentproduct: "Raleon Replenishment Product",
					dayssincelastsubscription: "Raleon Days Since Last Subscription",
					engagement30days: "Raleon Engagement 30 Days Score",
					engagement60days: "Raleon Engagement 60 Days Score",
					engagement90days: "Raleon Engagement 90 Days Score",
					loyaltysegment: "Raleon Loyalty Segment",
					winbackscore: "Raleon Winback Score",
					rebuypropensity: "Raleon Rebuy Propensity Score",
					rebuyproduct: "Raleon Rebuy Product",
					has_abandoned_checkout: "Raleon Has Abandoned Checkout",
					email_unique_opensl30: "Raleon Email Unique Opens Last 30 Days",
					email_unique_opensl60: "Raleon Email Unique Opens Last 60 Days",
					email_unique_opensl90: "Raleon Email Unique Opens Last 90 Days",
					email_total_opensl30: "Raleon Email Total Opens Last 30 Days",
					email_total_opensl60: "Raleon Email Total Opens Last 60 Days",
					email_total_opensl90: "Raleon Email Total Opens Last 90 Days",
    				fit_type: "Raleon Custom Fit Type",
					identityvaluesync: "Raleon Identity Value",
					raleonuserid: "Raleon User Id",
					subscriptionpropensity: "Raleon Subscription Propensity Score",
				})
			},
			{
				id: 3,
				name: "Stamped Reviews",
				category: "product-review",
				exchangeApiUrl: "",
				description: "Connect to your Stamped Reviews integration to enable Product Review Ways To Earn",
				oAuthURL: "",
				imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/stamped-logo.svg",
				docURL: "https://docs.raleon.io/docs/stamped-reviews",
				docURLText: "Stamped Documentation",
				enableDisableURL: "/integration/stamped/enable",
				showConnectButton: false,
				customComponent: "StampedReviews",
				canBeActivated: true,
				sortOrder: 900,
			},
			{
				id: 4,
				name: "Skio",
				category: "subscriptions",
				exchangeApiUrl: "",
				description: "Connect Skio to enable Subscription Ways To Earn",
				oAuthURL: "",
				imageURL: "https://raleoncdn.s3.amazonaws.com/Skio+logo.webp",
				docURL: "https://docs.raleon.io/docs/skio",
				docURLText: "Skio Documentation",
				enableDisableURL: "/integration/skio/enable",
				showConnectButton: false,
				customComponent: "Skio",
				canBeActivated: false,
				sortOrder: 800,
			},
			{
				id: 5,
				name: "Stay AI",
				category: "subscriptions",
				exchangeApiUrl: "",
				description: "Connect Stay AI to enable Subscription Ways To Earn",
				oAuthURL: "",
				imageURL: "https://raleoncdn.s3.amazonaws.com/Stay-logo.svg",
				docURL: "https://docs.raleon.io/docs/stayai-subscriptions",
				docURLText: "Stay Documentation",
				enableDisableURL: "/integration/stay/enable",
				showConnectButton: false,
				customComponent: "Stay",
				canBeActivated: false,
				sortOrder: 700,
			},
			{
				id: 6,
				name: "Prive",
				category: "subscriptions",
				exchangeApiUrl: "",
				description: "Connect Prive to enable Subscription Ways To Earn",
				oAuthURL: "",
				imageURL: "https://cdn.prod.website-files.com/6253aa39b0ef7854c36cf7f6/62618a9a8aa15bed70ffc851_prive-favicon.png",
				docURL: "https://docs.raleon.io/docs/prive",
				docURLText: "Prive Documentation",
				enableDisableURL: "/integration/prive/enable",
				showConnectButton: false,
				customComponent: "Prive",
				canBeActivated: false,
				sortOrder: 600,
			},
			{
				id: 8,
				name: "Loop",
				category: "subscriptions",
				exchangeApiUrl: "",
				description: "Connect Loop to enable Subscription Ways To Earn",
				oAuthURL: "",
				imageURL: "/client/images/loop.svg",
				docURL: "https://docs.raleon.io/docs/loop-subscriptions",
				docURLText: "Loop Documentation",
				enableDisableURL: "/integration/loop/enable",
				showConnectButton: false,
				customComponent: "Loop",
				canBeActivated: false,
				sortOrder: 500,
			},
			{
				id: 9,
				name: "Shopify",
				description: "Connect to Shopify to enable analytics and segmentation.",
				category: "ecommerce",
				exchangeApiUrl: "",
				oAuthURL: shopifyURL,
				imageURL: "https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg",
				docURL: "",
				docURLText: "",
				enableDisableURL: "",
				showConnectButton: false,
				customComponent: "ShopifyConnection",
				canBeActivated: false,
				sortOrder: 100,
				errorMessage: ""
			},
			// {
			// 	id: 7, name: "Sendlane",
			// 	description: "Sendlane is a powerful email marketing platform, utilize Raleon loyalty segments within Sendlane.",
			// 	category: "",
			// 	exchangeApiUrl: "",
			// 	oAuthURL: ``,
			// 	imageURL: "https://raleoncdn.s3.us-east-1.amazonaws.com/klaviyo.png",
			// 	docURL: "https://docs.raleon.io/docs/klavi",
			// 	docURLText: "Sendlane Documentation",
			// 	enableDisableURL: "",
			// 	customComponent: "Sendlane",
			// 	showConnectButton: false,
			// 	canBeActivated: true,
			// 	fieldMappings: JSON.stringify({
			// 		identityvalue: "customer",
			// 		referralcode: "Raleon Referral Code (Klaviyo)",
			// 		ltvdistribution: "Raleon LTV Distribution Score",
			// 		ltv: "Raleon LTV",
			// 		revenue: "Raleon Revenue",
			// 		replenishmentscore: "Raleon Replenishment Score",
			// 		churnrisk: "Raleon Churn Risk",
			// 		refundpropensity: "Raleon Refund Propensity Score",
			// 		discountscore: "Raleon Discount Score",
			// 		aov: "Raleon AOV",
			// 		totalorders: "Raleon Total Orders",
			// 		totalrefunds: "Raleon Total Refunds",
			// 		replenishmentproduct: "Raleon Replenishment Product",
			// 		dayssincelastsubscription: "Raleon Days Since Last Subscription",
			// 		engagement30days: "Raleon Engagement 30 Days Score",
			// 		loyaltysegment: "Raleon Loyalty Segment",
			// 		winbackscore: "Raleon Winback Score",
			// 		rebuypropensity: "Raleon Rebuy Propensity Score"
			// 	})
			// },
		]
		// Get all existing integrations in one query
		const existingIntegrations = await this.integrationRepo.find();
		const existingIntegrationsMap = new Map(existingIntegrations.map(i => [i.name, i]));

		for (const integration of integrations) {
			const existingIntegration = existingIntegrationsMap.get(integration.name);

			if (!existingIntegration) {
				await this.integrationRepo.create(integration);
				console.log(`Created new integration: ${integration.name}`);
			} else {
				console.log(`Integration ${integration.name} already exists, updating metadata`);
				//Forcing an update on the model
				await this.integrationRepo.updateById(existingIntegration.id, integration);
			}
		}
	}

	/**
	 * This method will be invoked when the application stops.
	 */
	async stop(): Promise<void> {
		// Add your logic for stop
	}
}
