import {injectable, BindingScope, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {PlannerCampaignRepository, TaskRepository, EmailGenerationRepository, UnlayerComponentRepository, PromptTemplateRepository, OrganizationPlannerPlanRepository, PlannerPlanVersionRepository, OrganizationRepository, OrganizationSegmentRepository, CampaignSegmentRepository} from '../../repositories';
import {PlannerCampaign, Task, TaskStep, TaskWithRelations, TaskStepWithRelations} from '../../models';
import {LLMRouterService} from '../chat/llm-router.service';
import {CompletionMessage, RouterParams, RouterResponse} from '../chat/types';
import {KlaviyoService} from '../integrations/klaviyo.service';
import {PromptContextService} from '../prompt/prompt-context.service';
import {PromptLogService} from '../prompt/prompt-log.service';
import {setNestedValue} from '../../utils/object-utils';
import {jsonrepair} from 'jsonrepair';
import {handleClaudeResponse} from '../chat/utils/campaign-parser';


@injectable({scope: BindingScope.TRANSIENT})
export class TaskService {
	constructor(
		@repository(PlannerCampaignRepository) private plannerCampaignRepository: PlannerCampaignRepository,
		@repository(TaskRepository) private taskRepository: TaskRepository,
		@repository(EmailGenerationRepository) private emailGenerationRepository: EmailGenerationRepository,
		@repository(UnlayerComponentRepository) private unlayerComponentRepository: UnlayerComponentRepository,
		@repository(PromptTemplateRepository) private promptTemplateRepository: PromptTemplateRepository,
		@repository(OrganizationPlannerPlanRepository) private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
		@repository(PlannerPlanVersionRepository) private plannerPlanVersionRepository: PlannerPlanVersionRepository,
		@repository(OrganizationRepository) private organizationRepository: OrganizationRepository,
		@repository(OrganizationSegmentRepository) private organizationSegmentRepository: OrganizationSegmentRepository,
		@repository(CampaignSegmentRepository) private campaignSegmentRepository: CampaignSegmentRepository,
		@service(LLMRouterService) private llmRouter: LLMRouterService,
		@service(KlaviyoService) private klaviyoService: KlaviyoService,
		@service(PromptContextService) private promptContextService: PromptContextService,
		@service(PromptLogService) private promptLogService: PromptLogService,
	) { }

	async createTaskForPlannerCampaign(plannerCampaign: PlannerCampaign): Promise<Task> {
		const plannerCampaignTemplate = await this.findTemplateForPlannerCampaign(plannerCampaign);

		const taskSteps = plannerCampaignTemplate.task.taskSteps.map((step) => ({
			...step,
			id: undefined,
			taskId: undefined
		}));

		const templateTask = plannerCampaignTemplate.task;
		delete templateTask.id;
		templateTask.taskSteps = undefined as any;
		delete (templateTask as any).taskSteps;


		const task = await this.taskRepository.create({
			...templateTask,
			plannerCampaignId: plannerCampaign.id,
			name: plannerCampaign.name,
			description: plannerCampaign.description,
			isTemplate: false,
			version: "0.9.2"
		});

		for (const step of taskSteps) {
			delete step.taskId;
			delete step.id;
			await this.taskRepository.taskSteps(task.id).create(step);
		}

		return task;
	}

	private async findTemplateForPlannerCampaign(plannerCampaign: PlannerCampaign): Promise<PlannerCampaign> {
		const matchingTemplate = await this.plannerCampaignRepository.findOne({
			where: {
				taskType: plannerCampaign.taskType,
				isTemplate: true
			},
			include: [
				{
					relation: 'task',
					scope: {
						include: [
							{
								relation: 'taskSteps'
							}
						]
					}
				}
			]
		});

		if (!matchingTemplate) {
			throw new Error('No matching task template found for planner campaign');
		}

		return matchingTemplate;
	}

	async postProcessEmailContent(task: TaskWithRelations, userPrompt?: string, templateType: string = 'Brief'): Promise<void> {
		// Get task with its steps
		const taskWithSteps = await this.taskRepository.findById(task.id, {
			include: [{
				relation: 'taskSteps'
			}]
		});

		// Find Content TaskStep if it exists
		const contentStep = (taskWithSteps.taskSteps as TaskStepWithRelations[])?.find(
			(step) => {
				const taskType = step?.name;
				return taskType === 'Content';
			}
		);

		if (contentStep) {
			// Update task status to Processing
			await this.taskRepository.updateById(task.id, {
				status: 'Processing'
			});

			const maxRetries = 5; // Maximum number of retries
			const initialDelay = 1000; // Initial delay in milliseconds (1 second)
			let retryCount = 0;

			// Get organization data for the email
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);


			while (retryCount < maxRetries) {
				const promptTemplate = await this.promptTemplateRepository.findOne({
					where: {
						type: templateType,
						isActive: true
					}
				});

				if (!promptTemplate) {
					throw new Error(`No active prompt template found for "${templateType}"`);
				}

				if (!plan.organizationId) {
					throw new Error('Organization ID is required');
				}

				const prompt = await this.promptContextService.replacePromptTags(promptTemplate.content, plan.organizationId, userPrompt, campaign, taskWithSteps);

				try {
					const messages: CompletionMessage[] = [{role: 'user', content: prompt}];
					const params: RouterParams = {
						models: ["openai/chatgpt-4o-latest", "openai/o3-mini"],
						maxTokens: 4096
					};
					const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
					const completion = response.content;

					// Store completion in Content TaskStep's data field
					await this.taskRepository.taskSteps(task.id).patch({
						data: completion
					}, {
						id: contentStep.id
					});

					await this.promptLogService.logPromptData(plan.organizationId, templateType, prompt, completion);

					// Update task status to Ready
					await this.taskRepository.updateById(task.id, {
						status: 'Ready'
					});

					// If successful, break out of retry loop
					break;

				} catch (error: any) {
					retryCount++;
					if (retryCount >= maxRetries) {
						console.error('Failed to get AI completion after maximum retries:', error);
						// Don't throw error, continue with other tasks
						break;
					}

					// Calculate exponential backoff delay
					const delay = initialDelay * Math.pow(2, retryCount - 1);
					console.warn(`Attempt ${retryCount} failed. Retrying in ${delay}ms...`);

					// Wait before retrying
					await new Promise(resolve => setTimeout(resolve, delay));
				}
			}
		}
	}

	async createUnlayerEmail(task: TaskWithRelations, templateType: string = 'GeneratedEmail'): Promise<any> {
		// Get related data
		const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
		const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
		const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

		const promptTemplate = await this.promptTemplateRepository.findOne({
			where: {
				type: templateType,
				isActive: true
			}
		});

		if (!promptTemplate) {
			throw new Error(`No active prompt template found for "${templateType}"`);
		}

		if (!plan.organizationId) {
			throw new Error('Organization ID is required');
		}
		console.log('promptTemplate', promptTemplate);
		const prompt = await this.promptContextService.replacePromptTags(promptTemplate.content, plan.organizationId, undefined, campaign, task);

		try {
			// Get completion from LLM Router
			const messages: CompletionMessage[] = [{role: 'user', content: prompt}];
			const params: RouterParams = {
				models: ["anthropic/claude-3.5-sonnet", "openai/o3-mini"],
				maxTokens: 4096
			};
			const response: RouterResponse = await this.llmRouter.executeCompletion(messages, params);
			const completion = response.content;

			await this.promptLogService.logPromptData(plan.organizationId, templateType, prompt, completion);

			// Parse the JSON response
			let cleaned = completion.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
			const firstBrace = cleaned.indexOf('{');
			const lastBrace = cleaned.lastIndexOf('}');
			if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
				cleaned = cleaned.substring(firstBrace, lastBrace + 1);
			}
			const repairedJsonString = jsonrepair(cleaned);
			const emailDesign = JSON.parse(repairedJsonString);

			//Find the EmailBase Component either overriden or not
			let emailBaseComponent = await this.unlayerComponentRepository.findOne({
				where: {
					name: 'EmailBase',
					orgId: plan.organizationId
				}
			});

			if (!emailBaseComponent) {
				emailBaseComponent = await this.unlayerComponentRepository.findOne({
					where: {
						name: 'EmailBase',
						orgId: {eq: null as any}
					}
				});
			}
			let emailComponents = []
			//For each item in emailDesign we need to look for the component and append
			for (const item of emailDesign.components) {
				let component = await this.unlayerComponentRepository.findOne({
					where: {
						name: item.name,
						orgId: plan.organizationId
					}
				});

				if (!component) {
					component = await this.unlayerComponentRepository.findOne({
						where: {
							name: item.name,
							orgId: {eq: null as any}
						}
					});
				}
				emailComponents.push(component);
			}

			//Lets assemble the email
			let emailJSON = emailBaseComponent?.json;
			let emailJSONFinal = emailJSON ? JSON.parse(emailJSON) : null;
			//Go through each component and add it to the emailJSON
			for (var i = 0; i < emailComponents.length; i++) {
				let emailBrandingComponent = emailDesign.components[i];
				let componentJSON = emailComponents[i]?.json;
				let componentObject = componentJSON ? JSON.parse(componentJSON) : null;


				//lets see if we have any editable fields
				if (emailBrandingComponent?.fields && emailBrandingComponent?.fields.length > 0) {
					for (const field of emailBrandingComponent.fields) {
						if (componentObject) {
							// Replace direct assignment with nested setter
							setNestedValue(componentObject, field.name, field.value);
						}
					}
				}

				//Add all body.rows to the emailJSONFinal body.rows array
				if (!emailJSONFinal.body.rows) {
					emailJSONFinal.body.rows = [];
				}

				if (componentObject?.body?.rows) {
					emailJSONFinal.body.rows = emailJSONFinal.body.rows.concat(componentObject.body.rows);
				}
			}

			// Validate the structure (basic validation)
			if (!emailJSONFinal.body || !emailJSONFinal.body.rows || !Array.isArray(emailJSONFinal.body.rows)) {
				console.error('Invalid email design structure:', emailJSONFinal);
				console.error('Retrying...');
				return this.createUnlayerEmail(task);
				//throw new Error('Invalid email design structure');
			}

			// Store the design in the task's data
			await this.taskRepository.updateById(task.id, {
				emailDesign: emailJSONFinal,
				status: 'In Progress'
			});

			return emailDesign;
		} catch (error: any) {
			console.error('Failed to create email design:', error);
			console.error('Retrying...');
			return this.createUnlayerEmail(task);
			//throw new Error('Failed to generate email design: ' + error.message);
		}
	}

	private convertToHybridTemplate(htmlContent: string): string {
		// Parse HTML and convert text and image content to Klaviyo hybrid blocks
		// Focus on table cell content as per Klaviyo documentation

		// Use a much more conservative approach to avoid breaking email structure
		// Only target specific, safe patterns for text and images

		// Pattern 1: Target text content in padding cells (common pattern in your email)
		htmlContent = htmlContent.replace(
			/<td([^>]*?padding[^>]*?)>\s*<div([^>]*?)>((?:[^<]|<(?!\/div>))*?)<\/div>\s*<\/td>/gi,
			(match, tdAttributes, divAttributes, divContent) => {
				// Skip if already has klaviyo-region
				if (tdAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Check if this is substantial text content
				const textContent = divContent.replace(/<[^>]*>/g, '').trim();
				const hasImage = /<img[^>]*>/i.test(divContent);
				const hasSubstantialText = textContent.length > 25; // Only substantial text

				if (hasSubstantialText && !hasImage) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
						<div class="klaviyo-block klaviyo-text-block">
							<div${divAttributes}>${divContent}</div>
						</div>
					</td>`;
				}

				return match;
			}
		);

		// Pattern 2: Target standalone image cells (simple image in td)
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*<img([^>]*?)>\s*<\/td>/gi,
			(match, attributes, imgAttributes) => {
				// Skip if already has klaviyo-region
				if (attributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Add align center if not present
				const newAttributes = attributes.includes('align=')
					? attributes
					: attributes + ' align="center"';

				return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
					<div class="klaviyo-block klaviyo-image-block">
						<img${imgAttributes}>
					</div>
				</td>`;
			}
		);

		// Pattern 3: Target table-wrapped images (common in email templates)
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*<table[^>]*?>\s*<tr>\s*<td[^>]*?>\s*<img([^>]*?)>\s*<\/td>\s*<\/tr>\s*<\/table>\s*<\/td>/gi,
			(match, outerAttributes, imgAttributes) => {
				// Skip if already has klaviyo-region
				if (outerAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Add align center if not present
				const newAttributes = outerAttributes.includes('align=')
					? outerAttributes
					: outerAttributes + ' align="center"';

				return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
					<div class="klaviyo-block klaviyo-image-block">
						<table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
							<tr>
								<td style="padding-right: 0px;padding-left: 0px;" align="center">
									<img${imgAttributes}>
								</td>
							</tr>
						</table>
					</div>
				</td>`;
			}
		);

		// Pattern 4: Target heading elements (H1, H2, H3, etc.) directly in table cells
		// Clean approach: extract just the heading and remove problematic MSO comments
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*(?:<!--\[if mso\]>.*?<!\[endif\]-->\s*)?<(h[1-6])([^>]*?)>((?:[^<]|<(?!\/\2>))*?)<\/\2>\s*(?:<!--\[if mso\]>.*?<!\[endif\]-->\s*)?<\/td>/gi,
			(match, tdAttributes, headingTag, headingAttributes, headingContent) => {
				// Skip if already has klaviyo-region
				if (tdAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Check if this has substantial text content
				const textContent = headingContent.replace(/<[^>]*>/g, '').trim();
				if (textContent.length > 3) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					// Create clean heading without MSO comments (Klaviyo handles Outlook compatibility)
					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
						<div class="klaviyo-block klaviyo-text-block">
							<${headingTag}${headingAttributes}>${headingContent}</${headingTag}>
						</div>
					</td>`;
				}

				return match;
			}
		);

		// Pattern 5: Target button/link content with complex MSO VML structures
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*(<!--\[if mso\]>.*?<!\[endif\]-->\s*)?<div[^>]*?align=["']?center["']?[^>]*?>\s*(<!--\[if mso\]>.*?<!\[endif\]-->\s*)?<a([^>]*?)href=["']([^"']*?)["']([^>]*?)>\s*<span([^>]*?)>(.*?)<\/span>\s*<\/a>\s*(<!--\[if mso\]>.*?<!\[endif\]-->\s*)?<\/div>\s*<\/td>/gi,
			(match, tdAttributes, msoStart, msoMiddle, aAttributesBefore, href, aAttributesAfter, spanAttributes, buttonText, msoEnd) => {
				// Skip if already has klaviyo-region
				if (tdAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Check if this has button text
				const textContent = buttonText.replace(/<[^>]*>/g, '').trim();
				if (textContent.length > 2) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					// Combine all anchor attributes and preserve original styling
					const fullAnchorAttributes = `${aAttributesBefore} href="${href}"${aAttributesAfter}`;

					// Create clean button preserving original styles
					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
				<div class="klaviyo-block klaviyo-text-block">
					<div align="center">
						<a${fullAnchorAttributes}>
							<span${spanAttributes}>${buttonText}</span>
						</a>
					</div>
				</div>
			</td>`;
				}

				return match;
			}
		);

		// Pattern 5b: Fallback for simpler button structures that might not have been caught
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>[\s\S]*?<a([^>]*?)href=["']([^"']*?)["']([^>]*?)>[\s\S]*?<span([^>]*?)>(.*?)<\/span>[\s\S]*?<\/a>[\s\S]*?<\/td>/gi,
			(match, tdAttributes, aAttributesBefore, href, aAttributesAfter, spanAttributes, buttonText) => {
				// Skip if already has klaviyo-region or if this contains a lot of other content
				if (tdAttributes.includes('data-klaviyo-region') || match.length > 2000) {
					return match;
				}

				// Check if this looks like a button (has span with text inside a link)
				const textContent = buttonText.replace(/<[^>]*>/g, '').trim();
				const isLikelyButton = textContent.length > 2 && textContent.length < 50;

				if (isLikelyButton) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					// Preserve original anchor and span styling
					const fullAnchorAttributes = `${aAttributesBefore} href="${href}"${aAttributesAfter}`;

					// Extract the div structure around the button if it exists
					const divMatch = match.match(/<div[^>]*?align=["']?center["']?[^>]*?>/i);
					const divTag = divMatch ? divMatch[0] : '<div align="center">';

					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
				<div class="klaviyo-block klaviyo-text-block">
					${divTag}
						<a${fullAnchorAttributes}>
							<span${spanAttributes}>${buttonText}</span>
						</a>
					</div>
				</div>
			</td>`;
				}

				return match;
			}
		);

		// Pattern 5c: Fallback for button structures with MSO comments
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*<!--\[if mso\]>.*?<!\[endif\]-->\s*<div align="center">\s*<!--\[if mso\]>.*?<!\[endif\]-->\s*<a([^>]*?)>\s*<span([^>]*?)>(.*?)<\/span>\s*<\/a>\s*<!--\[if mso\]>.*?<!\[endif\]-->\s*<\/div>\s*<\/td>/gi,
			(match, tdAttributes, anchorAttributes, spanAttributes, buttonText) => {
				// Skip if already has klaviyo-region
				if (tdAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				const textContent = buttonText.replace(/<[^>]*>/g, '').trim();
				if (textContent.length > 2) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
				<div class="klaviyo-block klaviyo-text-block">
					<div align="center">
						<a${anchorAttributes}>
							<span${spanAttributes}>${buttonText}</span>
						</a>
					</div>
				</div>
			</td>`;
				}

				return match;
			}
		);

		// Pattern 6: Target text content in simple div structures (broader than Pattern 1)
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*<div([^>]*?)>((?:[^<]|<(?!\/div>))*?)<\/div>\s*<\/td>/gi,
			(match, tdAttributes, divAttributes, divContent) => {
				// Skip if already has klaviyo-region
				if (tdAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Check if this is substantial text content (no images)
				const textContent = divContent.replace(/<[^>]*>/g, '').trim();
				const hasImage = /<img[^>]*>/i.test(divContent);
				const hasLink = /<a[^>]*>/i.test(divContent);
				const hasSubstantialText = textContent.length > 8; // Text like "WATER SAFETY FIRST"

				// Only convert if it's text content without complex nested structures
				if (hasSubstantialText && !hasImage && !hasLink) {
					// Add align center if not present
					const newAttributes = tdAttributes.includes('align=')
						? tdAttributes
						: tdAttributes + ' align="center"';

					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
						<div class="klaviyo-block klaviyo-text-block">
							<div${divAttributes}>${divContent}</div>
						</div>
					</td>`;
				}

				return match;
			}
		);

		// Pattern 7: Target linked images in complex table structures (like social media icons)
		htmlContent = htmlContent.replace(
			/<td([^>]*?)>\s*<table[^>]*?>\s*<tr>\s*<td[^>]*?>\s*<a[^>]*?>\s*<img([^>]*?)>\s*<\/a>\s*<\/td>\s*<\/tr>\s*<\/table>\s*<\/td>/gi,
			(match, outerAttributes, imgAttributes) => {
				// Skip if already has klaviyo-region
				if (outerAttributes.includes('data-klaviyo-region')) {
					return match;
				}

				// Extract the full link and image structure
				const linkMatch = match.match(/<a([^>]*?)>\s*<img([^>]*?)>\s*<\/a>/i);
				if (linkMatch) {
					const [, linkAttributes, fullImgAttributes] = linkMatch;

					// Add align center if not present
					const newAttributes = outerAttributes.includes('align=')
						? outerAttributes
						: outerAttributes + ' align="center"';

					return `<td${newAttributes} data-klaviyo-region="true" data-klaviyo-region-width-pixels="600">
						<div class="klaviyo-block klaviyo-image-block">
							<table role="presentation" width="100%" cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="padding-right: 0px;padding-left: 0px;" align="center">
										<a${linkAttributes}>
											<img${fullImgAttributes}>
										</a>
									</td>
								</tr>
							</table>
						</div>
					</td>`;
				}

				return match;
			}
		);

		return htmlContent;
	}

	async createKlaviyoCampaignAsync(
		taskId: number,
		orgId: number,
		htmlFromRequest?: string
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Check if we have HTML content - prioritize stored HTML, then request HTML
			let htmlContent = task.emailHtml || htmlFromRequest;

			if (!htmlContent) {
				throw new Error('No HTML content available. Please save the email design first.');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			// Get segment data - handle both Raleon and Klaviyo segments
			let segmentId = '';
			let segment = null;

			// Check if this campaign uses a Klaviyo segment
			const campaignSegments = await this.campaignSegmentRepository.find({
				where: {campaignId: campaign.id}
			});

			const klaviyoSegment = campaignSegments.find(cs => cs.segmentType === 'klaviyo' && cs.klaviyoSegmentId);

			if (klaviyoSegment) {
				// Use Klaviyo segment ID directly
				segmentId = klaviyoSegment.klaviyoSegmentId!;
				console.log('Using Klaviyo segment:', segmentId);
			} else {
				// Fall back to Raleon segment lookup
				segment = await this.organizationSegmentRepository.findOne({
					where: {
						orgId: plan.organizationId,
						name: campaign.targetSegment
					}
				});

				if (!segment || !segment.id || !segment.externalId) {
					console.log('Valid segment not found, using all subscribers');
					segmentId = '';
				} else {
					segmentId = segment.externalId.toString();
				}
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Converting to hybrid template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Convert HTML to hybrid template format (no AI processing)
			let processedHtml = this.convertToHybridTemplate(htmlContent);
			let explanation = "HTML converted to Klaviyo hybrid template format with editable text and image regions.";

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Extracting email metadata'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Create template using the processed HTML content
			const templateId = await this.klaviyoService.createTemplate(orgId, {
				name: `${campaign.name} Hybrid Template`,
				html: processedHtml,
				editorType: 'USER_DRAGGABLE'
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Create campaign and get both campaign ID and message ID
			const {campaignId, messageId} = await this.klaviyoService.createCampaign(orgId, {
				name: campaign.name,
				templateId: templateId,
				subject: subject,
				previewText: preview,
				listId: segmentId,
				sendTime: campaign.scheduledDate
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Assigning template to campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId,
				messageId,
				templateId
			});

			// Update task with Klaviyo IDs
			await this.taskRepository.updateById(taskId, {
				klaviyoTemplateId: templateId,
				klaviyoCampaignId: campaignId
			});

			// Update status to completed
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				data: {
					templateId,
					campaignId,
					messageId,
					hybridTemplate: true,
					explanation: explanation
				}
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});

		} catch (error: any) {
			console.error('Failed to create Klaviyo campaign:', error);
			// Update status to failed
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign'
			});
			throw error;
		}
	}

	async createKlaviyoCampaignHtmlOnlyAsync(
		taskId: number,
		orgId: number,
		htmlFromRequest?: string
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Check if we have HTML content - prioritize stored HTML, then request HTML
			let htmlContent = task.emailHtml || htmlFromRequest;

			if (!htmlContent) {
				throw new Error('No HTML content available. Please save the email design first.');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			// Get segment data - handle both Raleon and Klaviyo segments
			let segmentId = '';
			let segment = null;

			// Check if this campaign uses a Klaviyo segment
			const campaignSegments = await this.campaignSegmentRepository.find({
				where: {campaignId: campaign.id}
			});

			const klaviyoSegment = campaignSegments.find(cs => cs.segmentType === 'klaviyo' && cs.klaviyoSegmentId);

			if (klaviyoSegment) {
				// Use Klaviyo segment ID directly
				segmentId = klaviyoSegment.klaviyoSegmentId!;
				console.log('Using Klaviyo segment:', segmentId);
			} else {
				// Fall back to Raleon segment lookup
				segment = await this.organizationSegmentRepository.findOne({
					where: {
						orgId: plan.organizationId,
						name: campaign.targetSegment
					}
				});

				if (!segment || !segment.id || !segment.externalId) {
					console.log('Valid segment not found, using all subscribers');
					segmentId = '';
				} else {
					segmentId = segment.externalId.toString();
				}
			}

			// Update status - skip AI processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Extracting email metadata (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo template (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Create template using the original HTML content (no AI processing)
			const templateId = await this.klaviyoService.createTemplate(orgId, {
				name: `${campaign.name} Template (HTML-only)`,
				html: htmlContent, // Use original HTML without AI processing
				editorType: 'CODE' // Use CODE editor for HTML-only templates
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Creating Klaviyo campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Create campaign and get both campaign ID and message ID
			const {campaignId, messageId} = await this.klaviyoService.createCampaign(orgId, {
				name: campaign.name,
				templateId: templateId,
				subject: subject,
				previewText: preview,
				listId: segmentId,
				sendTime: campaign.scheduledDate
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Assigning template to campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId,
				messageId,
				templateId
			});

			// Update task with both template and campaign IDs
			await this.taskRepository.updateById(taskId, {
				klaviyoTemplateId: templateId,
				klaviyoCampaignId: campaignId
			});

			// Mark as completed
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'Campaign creation completed (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

		} catch (error: any) {
			console.error('Failed to create Klaviyo campaign (HTML-only):', error);
			// Update status to failed
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});
			throw error;
		}
	}

	async resyncKlaviyoCampaignAsync(
		taskId: number,
		orgId: number
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
			const version = await this.plannerPlanVersionRepository.findById(campaign.plannerPlanVersionId);
			const plan = await this.organizationPlannerPlanRepository.findById(version.organizationPlannerPlanId);

			if (!task.emailHtml) {
				throw new Error('No HTML content available for resync');
			}

			// Check if we have Klaviyo template and campaign IDs stored
			const contentStep = task.taskSteps?.find(step => step.taskTypeId === 3); // ID 3 is for email content
			if (!contentStep?.data) {
				throw new Error('No Klaviyo campaign data found');
			}

			// const klaviyoData = JSON.parse(contentStep.data);
			if (!task.klaviyoTemplateId || !task.klaviyoCampaignId) {
				throw new Error('Missing Klaviyo template or campaign ID');
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Converting to hybrid template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Convert HTML to hybrid template format (no AI processing)
			let processedHtml = this.convertToHybridTemplate(task.emailHtml);
			let explanation = "HTML converted to Klaviyo hybrid template format with editable text and image regions.";

			console.log('Using hybrid template conversion for resync (no AI processing)');

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo template'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Update the template
			await this.klaviyoService.updateTemplate(orgId, task.klaviyoTemplateId, {
				html: processedHtml
			});

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo campaign'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Determine send time - if the scheduled date is in the past, bump it to tomorrow
			let sendTime = campaign.scheduledDate;
			const scheduledDate = new Date(campaign.scheduledDate);
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (scheduledDate < today) {
				// If the date is in the past, set it to tomorrow at the same time
				const tomorrow = new Date();
				tomorrow.setDate(tomorrow.getDate() + 1);
				// Keep the original time if it was set, otherwise use current time
				const originalTime = new Date(campaign.scheduledDate);
				if (!isNaN(originalTime.getTime())) {
					tomorrow.setHours(originalTime.getHours(), originalTime.getMinutes(), originalTime.getSeconds());
				}
				sendTime = tomorrow.toISOString().split('T')[0]; // Format as YYYY-MM-DD

				// Update the campaign's scheduled date in our database to match
				await this.plannerCampaignRepository.updateById(campaign.id, {
					scheduledDate: sendTime
				});
			}

			// Update the campaign
			const data = await this.klaviyoService.updateCampaign(orgId, task.klaviyoCampaignId, {
				name: campaign.name,
				subject,
				previewText: preview,
				sendTime: sendTime
			});

			const messageId = data.data.relationships['campaign-messages'].data[0].id;
			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId: task.klaviyoCampaignId,
				messageId,
				templateId: task.klaviyoTemplateId
			});

			// Update status to complete
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'Resync completed successfully',
				data: {
					templateId: task.klaviyoTemplateId,
					campaignId: task.klaviyoCampaignId,
					messageId,
					htmlProcessed: true,
					explanation: explanation
				},
				startTime: new Date().toISOString()
			}, {
				taskId: taskId
			});

		} catch (error: any) {
			console.error('Error in resyncKlaviyoCampaignAsync:', error);
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message,
				startTime: new Date().toISOString()
			}, {
				taskId: taskId,
				operationType: 'klaviyo_resync'
			});
			throw error;
		}
	}

	async resyncKlaviyoCampaignHtmlOnlyAsync(
		taskId: number,
		orgId: number
	): Promise<void> {
		try {
			// Get task with all related data
			const task = await this.taskRepository.findById(taskId, {
				include: [{
					relation: 'taskSteps'
				}]
			});

			if (!task) {
				throw new Error('Task not found');
			}

			// Update status to processing
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Preparing campaign data (HTML-only resync)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Get campaign and related data
			const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);

			if (!task.emailHtml) {
				throw new Error('No HTML content available for resync');
			}

			// Check if we have Klaviyo campaign ID (template ID might be missing for older campaigns)
			if (!task.klaviyoCampaignId) {
				throw new Error('Missing Klaviyo campaign ID');
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo template (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Update or create template with original HTML (no AI processing)
			let templateId = task.klaviyoTemplateId;
			if (!templateId) {
				// Create a new template if one doesn't exist
				const campaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
				templateId = await this.klaviyoService.createTemplate(orgId, {
					name: `${campaign.name} Template (HTML-only)`,
					html: task.emailHtml,
					editorType: 'CODE'
				});
				// Update task with the new template ID
				await this.taskRepository.updateById(taskId, {
					klaviyoTemplateId: templateId
				});
			} else {
				// Update existing template
				await this.klaviyoService.updateTemplate(orgId, templateId, {
					html: task.emailHtml // Use original HTML without AI processing
				});
			}

			// Get email content from task step to extract subject/preview
			const emailStep = task.taskSteps.find(step => step.taskTypeId === 3);
			if (!emailStep?.data) {
				throw new Error('Missing email content data');
			}

			// Extract subject and preview from email content
			const emailContent = emailStep.data;
			let subject, preview;

			try {
				let cleaned = emailContent.replace(/^[\s`]*json[\s`]*|^[\s`]*|[\s`]*```[\s`]*$/gmi, '');
				const firstBrace = cleaned.indexOf('{');
				const lastBrace = cleaned.lastIndexOf('}');
				if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
					cleaned = cleaned.substring(firstBrace, lastBrace + 1);
				}
				const repairedJsonString = jsonrepair(cleaned);
				const parsedJson = JSON.parse(repairedJsonString);
				subject = parsedJson.subjectLine;
				preview = parsedJson.previewText;
			} catch (error) {
				//Fallback to markdown parsing
				const subjectMatch = emailContent.match(/\*\*subject\*\*(.*)/);
				const previewMatch = emailContent.match(/\*\*preview\*\*(.*)/);
				subject = subjectMatch?.[1]?.trim() || campaign.name;
				preview = previewMatch?.[1]?.trim() || '';
			}

			// Update status
			await this.emailGenerationRepository.updateAll({
				status: 'processing',
				step: 'Updating Klaviyo campaign (HTML-only)'
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

			// Determine send time - if the scheduled date is in the past, bump it to tomorrow
			let sendTime = campaign.scheduledDate;
			const scheduledDate = new Date(campaign.scheduledDate);
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (scheduledDate < today) {
				// If the date is in the past, set it to tomorrow at the same time
				const tomorrow = new Date();
				tomorrow.setDate(tomorrow.getDate() + 1);
				// Keep the original time if it was set, otherwise use current time
				const originalTime = new Date(campaign.scheduledDate);
				if (!isNaN(originalTime.getTime())) {
					tomorrow.setHours(originalTime.getHours(), originalTime.getMinutes(), originalTime.getSeconds());
				}
				sendTime = tomorrow.toISOString().split('T')[0]; // Format as YYYY-MM-DD

				// Update the campaign's scheduled date in our database to match
				await this.plannerCampaignRepository.updateById(campaign.id, {
					scheduledDate: sendTime
				});
			}

			// Update the campaign
			const data = await this.klaviyoService.updateCampaign(orgId, task.klaviyoCampaignId, {
				name: campaign.name,
				subject,
				previewText: preview,
				sendTime: sendTime
			});

			const messageId = data.data.relationships['campaign-messages'].data[0].id;
			// Assign the template to the campaign message
			await this.klaviyoService.assignTemplateToCampaign(orgId, {
				campaignId: task.klaviyoCampaignId,
				messageId,
				templateId: templateId
			});

			// Update status to complete
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				step: 'HTML-only resync completed successfully',
				data: {
					templateId: templateId,
					campaignId: task.klaviyoCampaignId,
					messageId,
					htmlProcessed: false, // No AI processing for HTML-only
					explanation: 'HTML was updated directly without AI processing'
				}
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});

		} catch (error: any) {
			console.error('Error in resyncKlaviyoCampaignHtmlOnlyAsync:', error);
			await this.emailGenerationRepository.updateAll({
				status: 'failed',
				error: error.message
			}, {
				taskId: taskId,
				operationType: 'klaviyo_campaign_html_only'
			});
			throw error;
		}
	}

	async generateEmailContentAsync(taskId: number, orgId: number): Promise<void> {
		try {
			const task = await this.taskRepository.findById(taskId, {
				include: [{relation: 'taskSteps'}]
			});
			if (!task) {
				throw new Error('Task not found');
			}
			console.log('Generating email content for task:', taskId);
			const emailDesign = await this.createUnlayerEmail(task);

			// Update generation record with success
			await this.emailGenerationRepository.updateAll({
				status: 'completed',
				design: emailDesign
			}, {
				taskId: taskId,
				operationType: 'email'
			});

		} catch (error: any) {
			console.error('Failed to generate email content:', error);
			// Update generation record with failure
			await this.emailGenerationRepository.updateAll({
				status: 'failed'
			}, {
				taskId: taskId,
				operationType: 'email'
			});
		}
	}
}
