import {inject, service} from '@loopback/core';
import {<PERSON>ronJob} from 'cron';
import {
  EmailWorkflow,
  EmailWorkflowGeneration,
  EmailWorkflowLog,
  EmailWorkflowStatus,
  EmailWorkflowGenerationStatus,
  EmailWorkflowLogLevel
} from '../models';
import {
  EmailWorkflowRepository,
  EmailWorkflowGenerationRepository,
  EmailWorkflowLogRepository,
  TaskRepository,
  PromptTemplateRepository,
  ConversationRepository,
  PlannerCampaignRepository,
  PlannerPlanVersionRepository,
  OrganizationPlannerPlanRepository
} from '../repositories';
import {ChatService} from './chat/chat.service';
import {ToolDefinition} from './chat/types';
import {ToolExecutorService} from './tool-executor.service';

export interface EmailWorkflowOptions {
  iterationCount?: number;
  timeoutMinutes?: number;
  tasteProfiles?: Array<{
    name: string;
  }>;
}

export interface BriefData {
  subjectLine: string;
  previewText: string;
  briefText: string;
}

export class EmailWorkflowService {
  constructor(
    @inject('repositories.EmailWorkflowRepository')
    private emailWorkflowRepository: EmailWorkflowRepository,
    @inject('repositories.EmailWorkflowGenerationRepository')
    private emailWorkflowGenerationRepository: EmailWorkflowGenerationRepository,
    @inject('repositories.EmailWorkflowLogRepository')
    private emailWorkflowLogRepository: EmailWorkflowLogRepository,
    @inject('repositories.TaskRepository')
    private taskRepository: TaskRepository,
    @inject('repositories.PromptTemplateRepository')
    private promptTemplateRepository: PromptTemplateRepository,
    @inject('repositories.ConversationRepository')
    private conversationRepository: ConversationRepository,
    @inject('repositories.PlannerCampaignRepository')
    private plannerCampaignRepository: PlannerCampaignRepository,
    @inject('repositories.PlannerPlanVersionRepository')
    private plannerPlanVersionRepository: PlannerPlanVersionRepository,
    @inject('repositories.OrganizationPlannerPlanRepository')
    private organizationPlannerPlanRepository: OrganizationPlannerPlanRepository,
    @service(ChatService)
    private chatService: ChatService,
    @service(ToolExecutorService)
    private toolExecutorService: ToolExecutorService,
  ) {
    // Start timeout check job - runs every minute
    new CronJob('*/1 * * * *', () => {
      this.checkForTimeouts().catch(console.error);
    }, null, true, 'America/New_York');
  }

  /**
   * Create a new email workflow
   */
  async createWorkflow(
    taskId: number,
    briefData: BriefData,
    options: EmailWorkflowOptions = {}
  ): Promise<EmailWorkflow> {
    const {
      iterationCount = 3,
      timeoutMinutes = 30,
      tasteProfiles = []
    } = options;

    // Calculate timeout
    const timeoutAt = new Date();
    timeoutAt.setMinutes(timeoutAt.getMinutes() + timeoutMinutes);

    // Create workflow
    const workflow = await this.emailWorkflowRepository.create({
      taskId,
      briefData,
      status: EmailWorkflowStatus.PENDING,
      iterationCount,
      completedIterations: 0,
      timeoutAt,
      metadata: {
        tasteProfiles: tasteProfiles.slice(0, iterationCount)
      }
    });

    // Create generation records
    for (let i = 0; i < iterationCount; i++) {
      await this.emailWorkflowGenerationRepository.create({
        workflowId: workflow.id!,
        iterationNumber: i + 1,
        status: EmailWorkflowGenerationStatus.PENDING,
        variationPrompt: {
          tasteProfile: tasteProfiles[i]?.name || 'Safe'
        }
      });
    }

    // Log creation
    await this.logWorkflowEvent(workflow.id!, {
      level: EmailWorkflowLogLevel.INFO,
      message: `Email workflow created with ${iterationCount} iterations`,
      step: 'workflow_creation',
      data: {
        taskId,
        iterationCount,
        timeoutMinutes,
        briefData: {
          subjectLine: briefData.subjectLine,
          previewText: briefData.previewText,
          briefTextLength: briefData.briefText.length
        }
      }
    });

    return workflow;
  }

  /**
   * Execute a workflow asynchronously
   */
  async executeWorkflow(workflowId: number): Promise<void> {
    const workflow = await this.emailWorkflowRepository.findById(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    // Update status to processing
    await this.emailWorkflowRepository.updateById(workflowId, {
      status: EmailWorkflowStatus.PROCESSING,
      startedAt: new Date()
    });

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Starting workflow execution',
      step: 'workflow_start'
    });

    try {
      // Execute workflow in background
      setImmediate(async () => {
        await this.processWorkflow(workflowId);
      });
    } catch (error) {
      await this.handleWorkflowError(workflowId, error, 'workflow_execution');
    }
  }

  /**
   * Process the workflow - generate all variations
   */
  private async processWorkflow(workflowId: number): Promise<void> {
    const startTime = Date.now();

    try {
      const workflow = await this.emailWorkflowRepository.findByIdWithRelations(workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      // Check if workflow has timed out
      if (new Date() > workflow.timeoutAt) {
        await this.markWorkflowAsTimedOut(workflowId);
        return;
      }

      const generations = await this.emailWorkflowGenerationRepository.findByWorkflowId(workflowId);

      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Processing ${generations.length} generations for workflow`,
        step: 'workflow_processing_start',
        data: {
          generationCount: generations.length,
          generationIds: generations.map(g => g.id)
        }
      });

      // Process each generation
      for (let i = 0; i < generations.length; i++) {
        const generation = generations[i];

        // Check timeout before each iteration
        if (new Date() > workflow.timeoutAt) {
          await this.markWorkflowAsTimedOut(workflowId);
          return;
        }

        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Processing generation ${i + 1} of ${generations.length} (iteration ${generation.iterationNumber})`,
          step: 'workflow_processing_generation',
          generationId: generation.id,
          data: {
            generationIndex: i + 1,
            totalGenerations: generations.length,
            iterationNumber: generation.iterationNumber
          }
        });

        await this.processGeneration(generation);
      }

      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Finished processing all ${generations.length} generations`,
        step: 'workflow_processing_complete',
        data: {
          generationCount: generations.length,
          duration: Date.now() - startTime
        }
      });

      // Check if all generations completed successfully
      const completedGenerations = await this.emailWorkflowGenerationRepository.find({
        where: {
          workflowId,
          status: EmailWorkflowGenerationStatus.COMPLETED
        }
      });

      // Get failed generations for reporting
      const failedGenerations = await this.emailWorkflowGenerationRepository.find({
        where: {
          workflowId,
          status: EmailWorkflowGenerationStatus.FAILED
        }
      });

      if (completedGenerations.length === workflow.iterationCount) {
        // All generations completed successfully
        await this.emailWorkflowRepository.updateById(workflowId, {
          status: EmailWorkflowStatus.COMPLETED,
          completedAt: new Date(),
          completedIterations: completedGenerations.length
        });

        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Workflow completed successfully. Generated ${completedGenerations.length} variations`,
          step: 'workflow_completion',
          duration: Date.now() - startTime,
          data: {
            completedIterations: completedGenerations.length,
            totalIterations: workflow.iterationCount
          }
        });
      } else if (completedGenerations.length > 0) {
        // Some generations completed successfully - mark as partial success
        await this.emailWorkflowRepository.updateById(workflowId, {
          status: EmailWorkflowStatus.COMPLETED,
          completedAt: new Date(),
          completedIterations: completedGenerations.length,
          error: `Partial success: ${completedGenerations.length}/${workflow.iterationCount} generations completed`
        });

        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Workflow completed with partial success. Generated ${completedGenerations.length}/${workflow.iterationCount} variations`,
          step: 'workflow_partial_completion',
          duration: Date.now() - startTime,
          data: {
            completedIterations: completedGenerations.length,
            totalIterations: workflow.iterationCount,
            failedIterations: failedGenerations.length
          }
        });
      } else {
        // No generations completed successfully
        await this.emailWorkflowRepository.updateById(workflowId, {
          status: EmailWorkflowStatus.FAILED,
          completedAt: new Date(),
          completedIterations: 0,
          error: 'No generations completed successfully'
        });

        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: `Workflow failed. No generations completed successfully`,
          step: 'workflow_failure',
          duration: Date.now() - startTime,
          data: {
            completedIterations: 0,
            totalIterations: workflow.iterationCount,
            failedIterations: failedGenerations.length
          }
        });
      }
    } catch (error) {
      await this.handleWorkflowError(workflowId, error, 'workflow_processing');
    }
  }

  /**
   * Process a single generation using taste profile-specific templates (RemoteEmail_Safe, RemoteEmail_Variety, etc.)
   */
  private async processGeneration(generation: EmailWorkflowGeneration): Promise<void> {
    const startTime = Date.now();

    try {
      // Update generation status
      await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
        status: EmailWorkflowGenerationStatus.PROCESSING,
        startedAt: new Date()
      });

      await this.logWorkflowEvent(generation.workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Starting generation ${generation.iterationNumber}`,
        step: 'generation_start',
        generationId: generation.id,
        data: {
          iterationNumber: generation.iterationNumber,
          variationPrompt: generation.variationPrompt
        }
      });

      // Get the workflow
      const workflow = await this.emailWorkflowRepository.findById(generation.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${generation.workflowId} not found`);
      }

      // Get the task to determine organization
      const task = await this.taskRepository.findById(workflow.taskId);
      if (!task) {
        throw new Error(`Task ${workflow.taskId} not found`);
      }

      // Get organization ID from task relationship
      const organizationId = await this.getOrganizationIdFromTask(task);

      // Determine template name based on taste profile
      const tasteProfile = generation.variationPrompt?.tasteProfile;
      const templateName = tasteProfile ? `RemoteEmail_${tasteProfile}` : 'RemoteEmail';

      // Get the appropriate template, fallback to default if taste-specific template not found
      let promptTemplate = await this.promptTemplateRepository.findOne({
        where: { name: templateName }
      });

      if (!promptTemplate && tasteProfile) {
        // Fallback to default RemoteEmail template if taste-specific template not found
        promptTemplate = await this.promptTemplateRepository.findOne({
          where: { name: 'RemoteEmail' }
        });
      }

      if (!promptTemplate) {
        throw new Error(`RemoteEmail prompt template not found (tried: ${templateName}${tasteProfile ? ', RemoteEmail' : ''})`);
      }

      // Create modified brief with variation prompt
      const modifiedBrief = this.createVariationBrief(workflow.briefData, generation.variationPrompt);

      // Define available tools and their executors for email workflow
      const availableTools: ToolDefinition[] = [
        {
          type: 'function',
          function: {
            name: 'image_lookup',
            description: 'Look up product images from the store catalog',
            parameters: {
              type: 'object',
              properties: {
                query: { type: 'string', description: 'Search query for images' }
              },
              required: ['query']
            }
          }
        },
        {
          type: 'function',
          function: {
            name: 'image_edit',
            description: 'Edit an existing image to add or modify text, objects, or general elements. Automatically detects the image aspect ratio and generates 3 variations. Useful for customizing promotional images, adding sale text, updating product information, or modifying visual elements like changing objects, colors, or styles.',
            parameters: {
              type: 'object',
              properties: {
                image_url: {
                  type: 'string',
                  description: 'URL of the image to edit'
                },
                new_text: {
                  type: 'string',
                  description: 'Text to add or replace on the image. Can include styling keywords like: "red text Hello", "bold large Sale", "blue italic Limited Time". If not provided, will perform non-text modifications.'
                },
                text_to_replace: {
                  type: 'string',
                  description: 'Optional existing text on the image to replace. If not provided, the AI will intelligently place the new text'
                },
                modification_description: {
                  type: 'string',
                  description: 'Description of non-text modifications to make. Examples: "change the dog to a golden retriever", "make the background blue", "add sunglasses to the person", "change the car color to red"'
                }
              },
              required: ['image_url']
            }
          }
        },
        {
          type: 'function',
          function: {
            name: 'product_lookup',
            description: 'Look up product information from the store catalog',
            parameters: {
              type: 'object',
              properties: {
                query: { type: 'string', description: 'Product search query' }
              },
              required: ['query']
            }
          }
        },
        {
          type: 'function',
          function: {
            name: 'best_sellers',
            description: 'Get best selling products from the store',
            parameters: {
              type: 'object',
              properties: {
                limit: { type: 'number', description: 'Number of products to return' }
              }
            }
          }
        }
      ];

      // Create tool executors with proper organization ID
      const baseToolExecutors = this.toolExecutorService.getToolExecutors();
      const toolExecutors: { [functionName: string]: (args: any) => Promise<any> } = {};

      // Wrap each tool executor to inject the organization ID
      for (const toolName of ['image_lookup', 'image_edit', 'product_lookup', 'best_sellers']) {
        if (baseToolExecutors[toolName]) {
          toolExecutors[toolName] = async (args: any) => {
            // Inject organization ID into the tool arguments
            const argsWithOrg = { ...args, orgId: organizationId };
            console.log(`🔧 EmailWorkflow calling tool ${toolName} with organization ID ${organizationId}`);
            return baseToolExecutors[toolName](argsWithOrg);
          };
        }
      }


      // Start conversation with taste profile-specific template and tools
      const { conversationId, messageId, completionPromise } = await this.chatService.startConversation(
        modifiedBrief,
        {
          organizationId,
          promptTemplateId: promptTemplate.id,
          routerParams: {
            systems: ['OpenRouter', 'Anthropic', 'OpenAI', '*'],
            models: ['anthropic/claude-sonnet-4','anthropic/claude-3.7-sonnet'],
            stream: false,
            tools: availableTools,
            toolExecutors: toolExecutors
          }
        }
      );

      await this.logWorkflowEvent(generation.workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Started conversation ${conversationId} for generation ${generation.iterationNumber}`,
        step: 'conversation_started',
        generationId: generation.id,
        data: {
          conversationId,
          messageId,
          briefLength: modifiedBrief.length
        }
      });

      // Wait for completion
      await completionPromise;

      await this.logWorkflowEvent(generation.workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Completion promise resolved for generation ${generation.iterationNumber}`,
        step: 'completion_promise_resolved',
        generationId: generation.id,
        data: {
          conversationId
        }
      });

      // Wait for the actual AI response to be available with polling
      let conversation = null;
      let lastMessage = null;
      let attempts = 0;
      const maxAttempts = 1000; // Wait up to 1000 seconds (~16.7 minutes) for image_edit operations

      while (attempts < maxAttempts) {
        await this.logWorkflowEvent(generation.workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Polling for AI response completion (attempt ${attempts + 1}/${maxAttempts})`,
          step: 'polling_for_response',
          generationId: generation.id,
          data: {
            conversationId,
            attempt: attempts + 1
          }
        });

        conversation = await this.conversationRepository.findById(conversationId, {
          include: [{ relation: 'messages', scope: { order: ['createdAt ASC'] } }]
        });
        lastMessage = conversation.messages?.[conversation.messages.length - 1];

        // Check if we have a complete AI response with final result marker
        if (lastMessage?.content && lastMessage.content.trim().length > 0) {
          // Check if the message contains the final result marker
          const hasFinalResult = lastMessage.content.includes('***FINAL_RESULT***');

          await this.logWorkflowEvent(generation.workflowId, {
            level: EmailWorkflowLogLevel.INFO,
            message: `AI response found after ${attempts + 1} attempts${hasFinalResult ? ' (contains FINAL_RESULT marker)' : ' (no FINAL_RESULT marker yet)'}`,
            step: 'ai_response_found',
            generationId: generation.id,
            data: {
              conversationId,
              messageCount: conversation.messages?.length || 0,
              lastMessageId: lastMessage?.id,
              contentLength: lastMessage.content.length,
              contentPreview: lastMessage.content.substring(0, 100) + '...',
              hasFinalResult
            }
          });

          // Only break if we have the final result marker
          if (hasFinalResult) {
            break;
          }
        }

        attempts++;

        // Wait 3 seconds before next attempt
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

      if (!lastMessage?.content || lastMessage.content.trim().length === 0) {
        await this.logWorkflowEvent(generation.workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: `No AI response found after ${maxAttempts} attempts`,
          step: 'ai_response_timeout',
          generationId: generation.id,
          data: {
            conversationId,
            messageCount: conversation?.messages?.length || 0,
            lastMessageId: lastMessage?.id,
            attempts: maxAttempts
          }
        });
        throw new Error('AI response not available after waiting');
      }

      await this.logWorkflowEvent(generation.workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Retrieved conversation for generation ${generation.iterationNumber}`,
        step: 'conversation_retrieved',
        generationId: generation.id,
        data: {
          conversationId,
          messageCount: conversation?.messages?.length || 0,
          lastMessageId: lastMessage?.id,
          lastMessageContent: lastMessage?.content ? lastMessage.content.substring(0, 100) + '...' : 'No content'
        }
      });

      if (!lastMessage?.content) {
        throw new Error(`No content generated from ${templateName} template`);
      }

      // Parse the email JSON from the conversation
      let emailComponents = null;
      try {
        await this.logWorkflowEvent(generation.workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Starting JSON parsing for generation ${generation.iterationNumber}`,
          step: 'json_parse_start',
          generationId: generation.id,
          data: {
            contentLength: lastMessage.content.length,
            contentPreview: lastMessage.content.substring(0, 200) + '...'
          }
        });

        console.log('AI Response content preview:', lastMessage.content.substring(0, 200) + '...');

        if (!generation.id) {
          throw new Error('Generation ID is undefined');
        }

        emailComponents = await this.extractJsonFromContent(lastMessage.content, generation.workflowId, generation.id);

        if (!emailComponents) {
          await this.logWorkflowEvent(generation.workflowId, {
            level: EmailWorkflowLogLevel.ERROR,
            message: `No valid JSON found in AI response for generation ${generation.iterationNumber}`,
            step: 'json_parse_no_result',
            generationId: generation.id,
            data: { content: lastMessage.content.substring(0, 500) }
          });
          console.error('Failed to extract JSON from AI response. Content:', lastMessage.content);
          throw new Error('No valid JSON found in the response');
        }

        // Detect the format type
        const formatType = this.detectEmailFormat(emailComponents);

        await this.logWorkflowEvent(generation.workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Successfully extracted email components for generation ${generation.iterationNumber} (${formatType} format)`,
          step: 'json_parse_success',
          generationId: generation.id,
          data: {
            formatType,
            componentKeys: Object.keys(emailComponents),
            componentCount: emailComponents.components?.length || 0,
            hasComponents: !!emailComponents.components,
            hasBody: !!emailComponents.body,
            hasCounters: !!emailComponents.counters,
            hasSubject: !!emailComponents.subject
          }
        });

        console.log('Successfully extracted email components:', Object.keys(emailComponents));
      } catch (parseError) {
        await this.logWorkflowEvent(generation.workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: `Failed to parse email components: ${parseError.message}`,
          step: 'json_parse_error',
          generationId: generation.id,
          data: {
            error: parseError.message,
            content: lastMessage.content.substring(0, 500),
            contentLength: lastMessage.content.length
          }
        });
        throw new Error(`Failed to parse email components: ${parseError.message}`);
      }

      // Update generation with results
      await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
        status: EmailWorkflowGenerationStatus.COMPLETED,
        completedAt: new Date(),
        emailDesignJson: emailComponents,
        finalHtml: JSON.stringify(emailComponents, null, 2), // Store formatted JSON
        imageProcessingData: {
          // Only include properties that exist in the model
          sectionsNeedingImages: [],
          processedImages: []
        }
      });

      // Update workflow completed iterations count
      const completedCount = await this.emailWorkflowGenerationRepository.count({
        workflowId: generation.workflowId,
        status: EmailWorkflowGenerationStatus.COMPLETED
      });

      await this.emailWorkflowRepository.updateById(generation.workflowId, {
        completedIterations: completedCount.count
      });

      await this.logWorkflowEvent(generation.workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Generation ${generation.iterationNumber} completed successfully`,
        step: 'generation_completion',
        generationId: generation.id,
        duration: Date.now() - startTime,
        data: {
          conversationId,
          hasEmailComponents: !!emailComponents,
          componentsCount: emailComponents?.components?.length || 0,
          completedIterations: completedCount.count
        }
      });

    } catch (error) {
      await this.handleGenerationError(generation, error);
    }
  }


  /**
   * Check for workflows that have exceeded timeout
   */
  private async checkForTimeouts(): Promise<void> {
    try {
      const timedOutWorkflows = await this.emailWorkflowRepository.findTimedOutWorkflows();

      for (const workflow of timedOutWorkflows) {
        await this.markWorkflowAsTimedOut(workflow.id!);
      }
    } catch (error) {
      console.error('Error checking for timeouts:', error);
    }
  }

  /**
   * Mark a workflow as timed out
   */
  private async markWorkflowAsTimedOut(workflowId: number): Promise<void> {
    await this.emailWorkflowRepository.updateById(workflowId, {
      status: EmailWorkflowStatus.TIMEOUT,
      completedAt: new Date(),
      error: 'Workflow exceeded maximum execution time of 30 minutes'
    });

    // Mark any processing generations as timed out
    const processingGenerations = await this.emailWorkflowGenerationRepository.find({
      where: {
        workflowId,
        status: EmailWorkflowGenerationStatus.PROCESSING
      }
    });

    for (const generation of processingGenerations) {
      await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
        status: EmailWorkflowGenerationStatus.TIMEOUT,
        completedAt: new Date(),
        error: 'Generation timed out'
      });
    }

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.ERROR,
      message: 'Workflow timed out after 30 minutes',
      step: 'workflow_timeout',
      data: {
        timeoutAt: new Date(),
        processingGenerations: processingGenerations.length
      }
    });
  }

  /**
   * Handle workflow errors
   */
  private async handleWorkflowError(
    workflowId: number,
    error: any,
    step: string
  ): Promise<void> {
    await this.emailWorkflowRepository.updateById(workflowId, {
      status: EmailWorkflowStatus.FAILED,
      completedAt: new Date(),
      error: error.message || 'Unknown error'
    });

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.ERROR,
      message: `Workflow failed: ${error.message}`,
      step,
      data: { error: error.message, stack: error.stack }
    });
  }

  /**
   * Handle generation errors
   */
  private async handleGenerationError(
    generation: EmailWorkflowGeneration,
    error: any
  ): Promise<void> {
    await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
      status: EmailWorkflowGenerationStatus.FAILED,
      completedAt: new Date(),
      error: error.message || 'Unknown error'
    });

    await this.logWorkflowEvent(generation.workflowId, {
      level: EmailWorkflowLogLevel.ERROR,
      message: `Generation ${generation.iterationNumber} failed: ${error.message}`,
      step: 'generation_error',
      generationId: generation.id,
      data: { error: error.message, stack: error.stack }
    });
  }

  /**
   * Log workflow events
   */
  private async logWorkflowEvent(
    workflowId: number,
    data: {
      level: EmailWorkflowLogLevel;
      message: string;
      step: string;
      generationId?: number;
      duration?: number;
      data?: any;
    }
  ): Promise<void> {
    try {
      await this.emailWorkflowLogRepository.createLog({
        workflowId,
        generationId: data.generationId,
        level: data.level,
        message: data.message,
        step: data.step,
        duration: data.duration,
        data: data.data
      });
    } catch (error) {
      console.error('Failed to log workflow event:', error);
    }
  }

  /**
   * Extracts JSON from AI response content that may be in various formats:
   * - Content after ***FINAL_RESULT*** marker (preferred)
   * - Wrapped in <email>...</email> tags
   * - Wrapped in json code blocks
   * - Wrapped in code blocks
   * - Plain JSON object starting with {
   * - Mixed with other text (explanatory text followed by JSON)
   */
  private async extractJsonFromContent(content: string, workflowId: number, generationId: number): Promise<any> {
    if (!content || typeof content !== 'string') {
      throw new Error('Content is empty or not a string');
    }

    console.log('🔍 Attempting to extract JSON from content (first 200 chars):', content.substring(0, 200));

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Starting JSON extraction with multiple strategies',
      step: 'json_extraction_start',
      generationId,
      data: {
        contentLength: content.length,
        contentStart: content.substring(0, 100)
      }
    });

    // Strategy 0: Look for content after ***FINAL_RESULT*** marker (highest priority)
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 0: content after ***FINAL_RESULT*** marker',
      step: 'json_strategy_0',
      generationId
    });

    const finalResultIndex = content.indexOf('***FINAL_RESULT***');
    if (finalResultIndex !== -1) {
      const contentAfterMarker = content.substring(finalResultIndex + '***FINAL_RESULT***'.length).trim();

      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Found ***FINAL_RESULT*** marker, extracting content after it',
        step: 'json_strategy_0_marker_found',
        generationId,
        data: {
          markerPosition: finalResultIndex,
          contentAfterMarker: contentAfterMarker.substring(0, 200)
        }
      });

      // Try to extract JSON from the content after the marker
      const jsonAfterMarker = await this.extractJsonFromContentRecursive(contentAfterMarker, workflowId, generationId, 'after ***FINAL_RESULT***');
      if (jsonAfterMarker) {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: 'SUCCESS: JSON extracted using Strategy 0: content after ***FINAL_RESULT*** marker',
          step: 'json_strategy_0_success',
          generationId,
          data: { extractedKeys: Object.keys(jsonAfterMarker) }
        });
        console.log('✅ JSON extracted using Strategy 0: content after ***FINAL_RESULT*** marker');
        return jsonAfterMarker;
      } else {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: 'FAILED: Strategy 0 - no valid JSON found after ***FINAL_RESULT*** marker',
          step: 'json_strategy_0_failed',
          generationId,
          data: { contentAfterMarker: contentAfterMarker.substring(0, 200) }
        });
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Strategy 0: No ***FINAL_RESULT*** marker found',
        step: 'json_strategy_0_no_marker',
        generationId
      });
    }

    // Fall back to original extraction methods
    return this.extractJsonFromContentRecursive(content, workflowId, generationId, 'fallback');
  }

  /**
   * Recursive helper method for JSON extraction strategies
   */
  private async extractJsonFromContentRecursive(content: string, workflowId: number, generationId: number, context: string): Promise<any> {
    console.log(`🔍 Attempting to extract JSON from content (${context}) (first 200 chars):`, content.substring(0, 200));

    // Strategy 1: Look for <email>...</email> tags
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: `Trying Strategy 1: <email> tags (${context})`,
      step: 'json_strategy_1',
      generationId
    });

    const emailTagMatch = content.match(/<email>\s*([\s\S]*?)\s*<\/email>/i);
    if (emailTagMatch) {
      try {
        const result = JSON.parse(emailTagMatch[1].trim());
        // Validate it looks like an email components structure, not a tool result
        if (this.isValidEmailDesignJson(result)) {
          await this.logWorkflowEvent(workflowId, {
            level: EmailWorkflowLogLevel.INFO,
            message: `SUCCESS: JSON extracted using Strategy 1: <email> tags (${context})`,
            step: 'json_strategy_1_success',
            generationId,
            data: { extractedKeys: Object.keys(result) }
          });
          console.log(`✅ JSON extracted using Strategy 1: <email> tags (${context})`);
          return result;
        }
      } catch (error) {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: `FAILED: Strategy 1 JSON parse error (${context})`,
          step: 'json_strategy_1_error',
          generationId,
          data: { error: error.message, content: emailTagMatch[1].substring(0, 200) }
        });
        console.warn(`Failed to parse JSON from <email> tags (${context}):`, error);
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Strategy 1: No <email> tags found (${context})`,
        step: 'json_strategy_1_no_match',
        generationId
      });
    }

    // Strategy 2: Look for json code blocks
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: `Trying Strategy 2: json code blocks (${context})`,
      step: 'json_strategy_2',
      generationId
    });

    const jsonCodeBlockMatch = content.match(/```json\s*([\s\S]*?)\s*```/i);
    if (jsonCodeBlockMatch) {
      try {
        const result = JSON.parse(jsonCodeBlockMatch[1].trim());
        // Validate it looks like an email components structure, not a tool result
        if (this.isValidEmailDesignJson(result)) {
          await this.logWorkflowEvent(workflowId, {
            level: EmailWorkflowLogLevel.INFO,
            message: `SUCCESS: JSON extracted using Strategy 2: json code blocks (${context})`,
            step: 'json_strategy_2_success',
            generationId,
            data: { extractedKeys: Object.keys(result) }
          });
          console.log('✅ JSON extracted using Strategy 2: json code blocks');
          return result;
        }
      } catch (error) {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: 'FAILED: Strategy 2 JSON parse error',
          step: 'json_strategy_2_error',
          generationId,
          data: { error: error.message, content: jsonCodeBlockMatch[1].substring(0, 200) }
        });
        console.warn('Failed to parse JSON from json code block:', error);
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Strategy 2: No json code blocks found',
        step: 'json_strategy_2_no_match',
        generationId
      });
    }

    // Strategy 3: Look for code blocks (without json specifier)
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 3: code blocks',
      step: 'json_strategy_3',
      generationId
    });

    const codeBlockMatch = content.match(/```\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch) {
      const blockContent = codeBlockMatch[1].trim();
      // Check if it looks like JSON (starts with { or [)
      if (blockContent.startsWith('{') || blockContent.startsWith('[')) {
        try {
          const result = JSON.parse(blockContent);
          await this.logWorkflowEvent(workflowId, {
            level: EmailWorkflowLogLevel.INFO,
            message: 'SUCCESS: JSON extracted using Strategy 3: code blocks',
            step: 'json_strategy_3_success',
            generationId,
            data: { extractedKeys: Object.keys(result) }
          });
          console.log('✅ JSON extracted using Strategy 3: code blocks');
          return result;
        } catch (error) {
          await this.logWorkflowEvent(workflowId, {
            level: EmailWorkflowLogLevel.ERROR,
            message: 'FAILED: Strategy 3 JSON parse error',
            step: 'json_strategy_3_error',
            generationId,
            data: { error: error.message, content: blockContent.substring(0, 200) }
          });
          console.warn('Failed to parse JSON from code block:', error);
        }
      } else {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: 'Strategy 3: Code block found but does not start with { or [',
          step: 'json_strategy_3_not_json',
          generationId,
          data: { blockStart: blockContent.substring(0, 50) }
        });
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Strategy 3: No code blocks found',
        step: 'json_strategy_3_no_match',
        generationId
      });
    }

    // Strategy 4: Look for the largest valid JSON object in the content
    // This handles cases where there's explanatory text before/after JSON
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 4: largest JSON object pattern',
      step: 'json_strategy_4',
      generationId
    });

    // Find all potential JSON objects by looking for opening braces and trying to match complete objects
    const allMatches: string[] = [];
    const openBraceRegex = /\{/g;
    let match;

    while ((match = openBraceRegex.exec(content)) !== null) {
      const startIndex = match.index;
      let braceCount = 1;
      let endIndex = startIndex + 1;

      // Find the matching closing brace
      while (endIndex < content.length && braceCount > 0) {
        const char = content[endIndex];
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
        }
        endIndex++;
      }

      if (braceCount === 0) {
        const jsonCandidate = content.substring(startIndex, endIndex);
        allMatches.push(jsonCandidate);
      }
    }

    if (allMatches && allMatches.length > 0) {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: `Strategy 4: Found ${allMatches.length} JSON-like objects`,
        step: 'json_strategy_4_matches',
        generationId,
        data: {
          matchCount: allMatches.length,
          matchLengths: allMatches.map(m => m.length)
        }
      });

      // Try each match, starting with the longest (most likely to be complete)
      const sortedMatches = allMatches.sort((a, b) => b.length - a.length);

      for (let i = 0; i < sortedMatches.length; i++) {
        const match = sortedMatches[i];
        try {
          const result = JSON.parse(match);
          // Validate it looks like an email components structure, not a tool result
          if (this.isValidEmailDesignJson(result)) {
            await this.logWorkflowEvent(workflowId, {
              level: EmailWorkflowLogLevel.INFO,
              message: `SUCCESS: JSON extracted using Strategy 4: largest JSON object pattern (candidate ${i + 1})`,
              step: 'json_strategy_4_success',
              generationId,
              data: {
                extractedKeys: Object.keys(result),
                matchLength: match.length,
                candidateIndex: i + 1
              }
            });
            console.log('✅ JSON extracted using Strategy 4: largest JSON object pattern');
            return result;
          }
        } catch (error) {
          await this.logWorkflowEvent(workflowId, {
            level: EmailWorkflowLogLevel.ERROR,
            message: `Strategy 4: JSON parse error for candidate ${i + 1}`,
            step: 'json_strategy_4_parse_error',
            generationId,
            data: {
              error: error.message,
              candidateIndex: i + 1,
              matchLength: match.length,
              content: match.substring(0, 200)
            }
          });
          console.warn('Failed to parse JSON object candidate:', error);
          continue;
        }
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Strategy 4: No JSON objects found',
        step: 'json_strategy_4_no_match',
        generationId
      });
    }

    // Strategy 5: Try to parse the entire content as JSON
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 5: entire content as JSON',
      step: 'json_strategy_5',
      generationId
    });

    try {
      const result = JSON.parse(content.trim());
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'SUCCESS: JSON extracted using Strategy 5: entire content as JSON',
        step: 'json_strategy_5_success',
        generationId,
        data: { extractedKeys: Object.keys(result) }
      });
      console.log('✅ JSON extracted using Strategy 5: entire content as JSON');
      return result;
    } catch (error) {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.ERROR,
        message: 'FAILED: Strategy 5 JSON parse error',
        step: 'json_strategy_5_error',
        generationId,
        data: { error: error.message, contentLength: content.length }
      });
      console.warn('Failed to parse entire content as JSON:', error);
    }

    // Strategy 6: Look for JSON array in the content (starts with [ and ends with ])
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 6: JSON array pattern',
      step: 'json_strategy_6',
      generationId
    });

    const jsonArrayMatch = content.match(/\[[\s\S]*\]/);
    if (jsonArrayMatch) {
      try {
        const result = JSON.parse(jsonArrayMatch[0]);
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: 'SUCCESS: JSON extracted using Strategy 6: JSON array pattern',
          step: 'json_strategy_6_success',
          generationId,
          data: { arrayLength: result.length }
        });
        console.log('✅ JSON extracted using Strategy 6: JSON array pattern');
        return result;
      } catch (error) {
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.ERROR,
          message: 'FAILED: Strategy 6 JSON parse error',
          step: 'json_strategy_6_error',
          generationId,
          data: { error: error.message, content: jsonArrayMatch[0].substring(0, 200) }
        });
        console.warn('Failed to parse JSON array from content:', error);
      }
    } else {
      await this.logWorkflowEvent(workflowId, {
        level: EmailWorkflowLogLevel.INFO,
        message: 'Strategy 6: No JSON array found',
        step: 'json_strategy_6_no_match',
        generationId
      });
    }

    // Strategy 7: Look for JSON after common intro phrases
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Trying Strategy 7: JSON after intro phrases',
      step: 'json_strategy_7',
      generationId
    });

    const introPhrases = [
      /here's the email.*?:/i,
      /here is the email.*?:/i,
      /email.*?:/i,
      /components.*?:/i,
      /design.*?:/i
    ];

    for (let i = 0; i < introPhrases.length; i++) {
      const phrase = introPhrases[i];
      const splitMatch = content.split(phrase);
      if (splitMatch.length > 1) {
        const afterIntro = splitMatch[1].trim();
        await this.logWorkflowEvent(workflowId, {
          level: EmailWorkflowLogLevel.INFO,
          message: `Strategy 7: Found intro phrase ${i + 1}, parsing content after it`,
          step: 'json_strategy_7_phrase_found',
          generationId,
          data: {
            phraseIndex: i + 1,
            contentAfterIntro: afterIntro.substring(0, 100)
          }
        });
        console.log('🔍 Trying to parse content after intro phrase:', afterIntro.substring(0, 100));

        // Look for JSON in the remaining content
        const jsonMatch = afterIntro.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            const result = JSON.parse(jsonMatch[0]);
            await this.logWorkflowEvent(workflowId, {
              level: EmailWorkflowLogLevel.INFO,
              message: `SUCCESS: JSON extracted using Strategy 7: after intro phrase ${i + 1}`,
              step: 'json_strategy_7_success',
              generationId,
              data: {
                extractedKeys: Object.keys(result),
                phraseIndex: i + 1
              }
            });
            console.log('✅ JSON extracted using Strategy 7: after intro phrase');
            return result;
          } catch (error) {
            await this.logWorkflowEvent(workflowId, {
              level: EmailWorkflowLogLevel.ERROR,
              message: `Strategy 7: JSON parse error after intro phrase ${i + 1}`,
              step: 'json_strategy_7_parse_error',
              generationId,
              data: {
                error: error.message,
                phraseIndex: i + 1,
                content: jsonMatch[0].substring(0, 200)
              }
            });
            console.warn('Failed to parse JSON after intro phrase:', error);
          }
        }
      }
    }

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Strategy 7: No intro phrases found',
      step: 'json_strategy_7_no_match',
      generationId
    });

    // If all strategies fail, return null
    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.ERROR,
      message: 'ALL JSON extraction strategies failed - no valid JSON found in content',
      step: 'json_extraction_complete_failure',
      generationId,
      data: {
        contentLength: content.length,
        strategiesAttempted: 7,
        contentPreview: content.substring(0, 300)
      }
    });

    console.error('❌ All JSON extraction strategies failed');
    return null;
  }

  /**
   * Validate if a JSON object is a valid email design structure
   * Supports both component-based format and raw Unlayer format
   */
  private isValidEmailDesignJson(obj: any): boolean {
    if (!obj || typeof obj !== 'object') {
      return false;
    }

    // Exclude tool results - these have status and message fields
    if (obj.status && obj.message) {
      console.log('❌ Rejecting object with status/message fields (tool result):', Object.keys(obj));
      return false;
    }

    // Exclude product lookup results - these have products array and status
    if (obj.products && obj.status) {
      console.log('❌ Rejecting object with products/status fields (product lookup result):', Object.keys(obj));
      return false;
    }

    // Exclude image lookup results - these have images array and status
    if (obj.images && obj.status) {
      console.log('❌ Rejecting object with images/status fields (image lookup result):', Object.keys(obj));
      return false;
    }

    // Exclude objects that have "note" field (often tool results)
    if (obj.note && typeof obj.note === 'string') {
      console.log('❌ Rejecting object with note field (likely tool result):', Object.keys(obj));
      return false;
    }

    // Check for raw Unlayer format (counters + body structure)
    if (obj.counters && obj.body && typeof obj.counters === 'object' && typeof obj.body === 'object') {
      console.log('✅ Accepting object with Unlayer format (counters + body):', Object.keys(obj));
      return true;
    }

    // Accept objects that have email-related fields (component-based format)
    const emailFields = ['components', 'subject', 'body', 'html', 'content', 'sections', 'blocks', 'header', 'footer'];
    const hasEmailField = emailFields.some(field => obj.hasOwnProperty(field));

    if (hasEmailField) {
      console.log('✅ Accepting object with email-related fields:', Object.keys(obj));
      return true;
    }

    // For objects without clear email fields, be more strict
    // Only accept if it looks like a well-structured email design
    const keys = Object.keys(obj);
    const hasReasonableEmailStructure = keys.length > 1 &&
      !keys.includes('status') &&
      !keys.includes('message') &&
      !keys.includes('products') &&
      !keys.includes('images') &&
      !keys.includes('note');

    if (hasReasonableEmailStructure) {
      console.log('✅ Accepting object with reasonable email structure:', keys);
      return true;
    }

    console.log('❌ Rejecting object - does not match email design patterns:', keys);
    return false;
  }

  /**
   * Detect the format type of the email design JSON
   */
  private detectEmailFormat(obj: any): string {
    if (!obj || typeof obj !== 'object') {
      return 'unknown';
    }

    // Check for raw Unlayer format (counters + body structure)
    if (obj.counters && obj.body && typeof obj.counters === 'object' && typeof obj.body === 'object') {
      return 'unlayer';
    }

    // Check for component-based format
    if (obj.components && Array.isArray(obj.components)) {
      return 'components';
    }

    // Check for other email-related structures
    const emailFields = ['subject', 'html', 'content', 'sections', 'blocks', 'header', 'footer'];
    const hasEmailField = emailFields.some(field => obj.hasOwnProperty(field));

    if (hasEmailField) {
      return 'email-structure';
    }

    return 'unknown';
  }

  /**
   * Get organization ID from task relationship
   * Task -> PlannerCampaign -> PlannerPlanVersion -> OrganizationPlannerPlan -> organizationId
   */
  private async getOrganizationIdFromTask(task: any): Promise<number> {
    try {
      if (!task.plannerCampaignId) {
        console.warn('Task has no plannerCampaignId, defaulting to organization ID 1');
        return 1;
      }

      // Get the planner campaign
      const plannerCampaign = await this.plannerCampaignRepository.findById(task.plannerCampaignId);
      if (!plannerCampaign || !plannerCampaign.plannerPlanVersionId) {
        console.warn('PlannerCampaign not found or has no plannerPlanVersionId, defaulting to organization ID 1');
        return 1;
      }

      // Get the planner plan version
      const plannerPlanVersion = await this.plannerPlanVersionRepository.findById(plannerCampaign.plannerPlanVersionId);
      if (!plannerPlanVersion || !plannerPlanVersion.organizationPlannerPlanId) {
        console.warn('PlannerPlanVersion not found or has no organizationPlannerPlanId, defaulting to organization ID 1');
        return 1;
      }

      // Get the organization planner plan
      const organizationPlannerPlan = await this.organizationPlannerPlanRepository.findById(plannerPlanVersion.organizationPlannerPlanId);
      if (!organizationPlannerPlan || !organizationPlannerPlan.organizationId) {
        console.warn('OrganizationPlannerPlan not found or has no organizationId, defaulting to organization ID 1');
        return 1;
      }

      const organizationId = organizationPlannerPlan.organizationId;
      console.log(`✅ Successfully retrieved organization ID ${organizationId} from task ${task.id}`);
      return organizationId;
    } catch (error) {
      console.error('Error getting organization ID from task:', error);
      console.warn('Defaulting to organization ID 1 due to error');
      return 1;
    }
  }

  /**
   * Create variation brief for email generation
   * Note: Taste profile variations are handled by different prompt templates (RemoteEmail_Safe, RemoteEmail_Variety, etc.)
   */
  private createVariationBrief(
    originalBrief: BriefData,
    variationPrompt?: {
      tasteProfile?: string;
    }
  ): string {
    // Return the original brief as-is since taste profile variations are handled by different templates
    return originalBrief.briefText;
  }

  /**
   * Get workflow by ID with all relations
   */
  async getWorkflowById(id: number): Promise<EmailWorkflow | null> {
    return this.emailWorkflowRepository.findByIdWithRelations(id);
  }

  /**
   * Get workflows by task ID
   */
  async getWorkflowsByTaskId(taskId: number): Promise<EmailWorkflow[]> {
    return this.emailWorkflowRepository.findByTaskId(taskId);
  }

  /**
   * Get workflow logs
   */
  async getWorkflowLogs(workflowId: number): Promise<EmailWorkflowLog[]> {
    return this.emailWorkflowLogRepository.findByWorkflowId(workflowId);
  }

  /**
   * Get active workflows
   */
  async getActiveWorkflows(): Promise<EmailWorkflow[]> {
    return this.emailWorkflowRepository.findActiveWorkflows();
  }

  /**
   * Retry a failed workflow
   */
  async retryWorkflow(workflowId: number): Promise<void> {
    const workflow = await this.emailWorkflowRepository.findById(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }

    if (workflow.status !== EmailWorkflowStatus.FAILED && workflow.status !== EmailWorkflowStatus.TIMEOUT) {
      throw new Error(`Workflow ${workflowId} is not in a failed or timeout state`);
    }

    // Reset workflow status
    const newTimeoutAt = new Date();
    newTimeoutAt.setMinutes(newTimeoutAt.getMinutes() + 30);

    await this.emailWorkflowRepository.updateById(workflowId, {
      status: EmailWorkflowStatus.PENDING,
      startedAt: undefined,
      completedAt: undefined,
      timeoutAt: newTimeoutAt,
      error: undefined,
      completedIterations: 0
    });

    // Reset failed generations
    const failedGenerations = await this.emailWorkflowGenerationRepository.find({
      where: {
        workflowId,
        status: {
          inq: [EmailWorkflowGenerationStatus.FAILED, EmailWorkflowGenerationStatus.TIMEOUT]
        }
      }
    });

    for (const generation of failedGenerations) {
      await this.emailWorkflowGenerationRepository.updateById(generation.id!, {
        status: EmailWorkflowGenerationStatus.PENDING,
        startedAt: undefined,
        completedAt: undefined,
        error: undefined,
        emailDesignJson: undefined,
        finalHtml: undefined
      });
    }

    await this.logWorkflowEvent(workflowId, {
      level: EmailWorkflowLogLevel.INFO,
      message: 'Workflow retry initiated',
      step: 'workflow_retry',
      data: {
        previousStatus: workflow.status,
        retriedGenerations: failedGenerations.length
      }
    });

    // Execute the workflow
    await this.executeWorkflow(workflowId);
  }

}
