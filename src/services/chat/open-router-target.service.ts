import {injectable, inject, BindingScope} from '@loopback/core';
import {CompletionMessage, RouterParams, StreamingCallback, ToolCall} from './types';
import {BaseRouterTarget} from './base-router-target';
import {request} from 'http';

interface OpenRouterResponse {
    id?: string;
    choices: Array<{
        message?: {
            content: string;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
        delta?: {
            content: string;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
    }>;
}

interface OpenRouterGenerationDetails {
	id: string;
	total_cost: number;
    created_at: string;
    model: string;
    origin: string;
    usage: number;
    is_byok: boolean;
    upstream_id: string;
    cache_discount: number;
    app_id: number;
    streamed: boolean;
    cancelled: boolean;
    provider_name: string;
    latency: number;
    moderation_latency: number;
    generation_time: number;
    finish_reason: string;
    native_finish_reason: string;
    tokens_prompt: number;
    tokens_completion: number;
    native_tokens_prompt: number;
    native_tokens_completion: number;
    native_tokens_reasoning: number;
    num_media_prompt: number;
    num_media_completion: number;
    num_search_results: number;
}

@injectable({scope: BindingScope.TRANSIENT})
export class OpenRouterTarget extends BaseRouterTarget {
	id = 'openrouter';
	systemId = 'OpenRouter';
	// Support all major providers through OpenRouter
	supportedModels = ['*'];
	supportedProviders = ['*'];

	private readonly apiKey: string;
	private readonly baseURL = 'https://openrouter.ai/api/v1';

	constructor() {
		super();
		this.apiKey = '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'; //process.env.OPENROUTER_API_KEY || '';
		if (!this.apiKey) {
			console.warn('OpenRouter API key not found in environment variables');
		}
	}

	protected supportsStreaming(): boolean {
		return true;
	}

	private async fetchGenerationDetails(id: string, retries = 3, delay = 1000): Promise<{
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
	}> {
		// Initial delay to allow the generation details to be available
		await new Promise(resolve => setTimeout(resolve, delay));

		for (let attempt = 0; attempt < retries; attempt++) {
			try {
				const response = await fetch(`${this.baseURL}/generation?id=${id}`, {
					headers: {
						'Authorization': `Bearer ${this.apiKey}`,
						'HTTP-Referer': 'https://raleon.ai',
						'X-Title': 'Raleon AI'
					}
				});

				if (response.status === 404) {
					if (attempt < retries - 1) {
						// Wait before retrying
						await new Promise(resolve => setTimeout(resolve, delay));
						continue;
					}
					return {
						provider: 'unknown',
						model: 'unknown',
						additional: { error: 'Generation details not yet available' }
					};
				}

				if (!response.ok) {
					throw new Error('Failed to fetch generation details' + response.statusText + ' ' + response.status);
				}

				const data = (await response.json() as any).data as OpenRouterGenerationDetails;
				return {
					provider: data.provider_name || 'unknown',
					model: data.model || 'unknown',
					totalCost: data.total_cost,
					promptTokens: data.tokens_prompt,
					completionTokens: data.tokens_completion,
					totalTokens: data.tokens_prompt + data.tokens_completion,
					tokenRatePerSec: undefined, // OpenRouter doesn't provide this directly
					firstTokenMs: undefined, // OpenRouter doesn't provide this directly
					latencyMs: data.latency,
					generationTimeMs: data.generation_time,
					finishReason: data.finish_reason,
					additional: {
						origin: data.origin,
						isByok: data.is_byok,
						upstreamId: data.upstream_id,
						cacheDiscount: data.cache_discount,
						appId: data.app_id,
						streamed: data.streamed,
						cancelled: data.cancelled,
						moderationLatency: data.moderation_latency,
						nativeFinishReason: data.native_finish_reason,
						nativeTokensPrompt: data.native_tokens_prompt,
						nativeTokensCompletion: data.native_tokens_completion,
						nativeTokensReasoning: data.native_tokens_reasoning,
						numMediaPrompt: data.num_media_prompt,
						numMediaCompletion: data.num_media_completion,
						numSearchResults: data.num_search_results
					}
				};
			} catch (error) {
				if (attempt === retries - 1) {
					console.error('Failed to fetch generation details after retries:', error);
					return {
						provider: 'unknown',
						model: 'unknown',
						additional: { error: error instanceof Error ? error.message : 'Unknown error' }
					};
				}
				await new Promise(resolve => setTimeout(resolve, delay));
			}
		}

		return {
			provider: 'unknown',
			model: 'unknown',
			additional: { error: 'Failed to fetch after retries' }
		};
	}

	protected async executeCompletion(
		messages: CompletionMessage[],
		params: RouterParams,
		onChunk?: StreamingCallback
	): Promise<{
		content: string;
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
	}> {
		return this.makeRequest(messages, params, params.stream || false, onChunk);
	}

	protected async executeStreamingCompletion(
		messages: CompletionMessage[],
		params: RouterParams,
		onChunk: StreamingCallback
	): Promise<{content: string; provider: string; model: string}> {
		return this.makeRequest(messages, params, true, onChunk);
	}

	private async makeRequest(
		messages: CompletionMessage[],
		params: RouterParams,
		stream: boolean,
		onChunk?: StreamingCallback
	): Promise<{
		content: string;
		provider: string;
		model: string;
		totalCost?: number;
		promptTokens?: number;
		completionTokens?: number;
		totalTokens?: number;
		tokenRatePerSec?: number;
		firstTokenMs?: number;
		latencyMs?: number;
		generationTimeMs?: number;
		finishReason?: string;
		additional?: Record<string, unknown>;
		toolCalls?: ToolCall[];
	}> {
		if (!this.apiKey) {
			throw new Error('OpenRouter API key not configured');
		}

		// Filter out any tool messages that don't have a valid toolCallId or name
		const filteredMessages = messages.filter(msg =>
			msg.role !== 'tool' || (msg.toolCallId && msg.name)
		);

		// Make sure tool messages appear after assistant messages with tool_calls
		const validMessages = [];
		let lastAssistantHadToolCalls = false;

		for (const msg of filteredMessages) {
			if (msg.role === 'assistant' && msg.tool_calls && msg.tool_calls.length > 0) {
				lastAssistantHadToolCalls = true;
				validMessages.push(msg);
			} else if (msg.role === 'tool') {
				if (lastAssistantHadToolCalls) {
					validMessages.push(msg);
				} else {
					// Skip tool messages that don't follow an assistant with tool calls
					console.warn('Skipping orphaned tool message', msg.toolCallId, msg.name);
				}
			} else {
				lastAssistantHadToolCalls = false;
				validMessages.push(msg);
			}
		}


		// Log the final message sequence for debugging
		console.log('Sending messages to OpenRouter:',
			validMessages.map(msg => ({role: msg.role, toolCall: msg.toolCallId ? 'yes' : 'no'})));

		// Find the first 4 messages over 5000 characters to cache (in order)
		const messagesToCache = [];
		if(params.models?.[0].includes('claude')) {
			for (let i = 0; i < validMessages.length && messagesToCache.length < 4; i++) {
				const msg = validMessages[i];
				if (typeof msg.content === 'string' && msg.content.length > 5000) {
					messagesToCache.push({
						index: i,
						length: msg.content.length
					});
				}
			}
		}

		const indicesToCache = new Set(messagesToCache.map(item => item.index));

		// Debug cache_control - log which messages will be cached
		// messagesToCache.forEach((item, cacheIndex) => {
		// 	console.log(`Message ${item.index} content length ${item.length} > 5000, will be cached (${cacheIndex + 1}/4)`);
		// });

		const requestBody: any = {
			messages: validMessages.map((msg, index) => {
				const messageObj: any = {
					role: msg.role,
					tool_call_id: msg.toolCallId,
					name: msg.name,
					tool_calls: msg.tool_calls
				};

				// Convert content to array format with cache_control only for the 4 largest messages
				if (Array.isArray(msg.content)) {
					// Already array format, preserve as-is
					messageObj.content = msg.content;
				} else if (typeof msg.content === 'string' && indicesToCache.has(index)) {
					// Convert large string content to array format with cache_control
					messageObj.content = [{
						type: 'text',
						text: msg.content,
						cache_control: {
							type: 'ephemeral'
						}
					}];
				} else {
					// All other content, keep as string
					messageObj.content = msg.content;
				}

				return messageObj;
			}),
			stream,
			tools: params.tools?.map(tool => ({
				type: tool.type,
				function: {
					name: tool.function.name,
					description: tool.function.description,
					parameters: tool.function.parameters
				}
			}))
		};

		if (params.models?.[0]) {
			requestBody.models = params.models;
		} else {
			requestBody.models = ['openrouter/auto'];
		}

		if (params.providers?.[0] && params.providers[0] !== '*') {
			requestBody.provider = {
				order: params.providers
			};
		}

		if(requestBody.tools?.length === 0) {
			delete requestBody.tools;
		}

		// Debug: Log final request body to verify cache_control is included
		console.log('OpenRouter request body:', JSON.stringify(requestBody, null, 2));

		const fetchResponse = await fetch(`${this.baseURL}/chat/completions`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Authorization': `Bearer ${this.apiKey}`,
				'HTTP-Referer': 'https://raleon.ai',
				'X-Title': 'Raleon AI'
			},
			body: JSON.stringify(requestBody)
		});

		if (!fetchResponse.ok) {
			const error = await fetchResponse.text();
			throw new Error(`OpenRouter API error: ${error}`);
		}

		if (!stream) {
			const data = await fetchResponse.json() as OpenRouterResponse & { id?: string };
			if (!data.choices?.[0]?.message) {
				throw new Error('Invalid response from OpenRouter API');
			}

			const message = data.choices[0].message;
			const content = message.content || '';
			const toolCalls = message.tool_calls?.map(tc => ({
				type: tc.type as 'function',
				id: tc.id,
				function: {
					name: tc.function.name,
					arguments: tc.function.arguments
				}
			}));

			const metadata = {
				provider: 'openrouter',
				model: 'pending'
			};

			// Start metadata fetch in background if we have an ID
			if (data.id) {
				this.fetchGenerationDetails(data.id)
					.then(details => {
						if (onChunk) {
							onChunk({
								content: '',
								done: true,
								metadata: {
									system: this.systemId,
									...details
								},
								toolCalls
							}).catch(console.error);
						}
					})
					.catch(console.error);
			}

			return {
				content,
				...metadata,
				toolCalls
			};
		}

		// Handle streaming response
		const reader = fetchResponse.body?.getReader();
		if (!reader) {
			throw new Error('Failed to get stream reader');
		}

		let fullResponse = '';
		let generationId: string | undefined;
		const toolCallAggregator: { [key: string]: ToolCall } = {}; // Aggregate by ID
		let aggregatedToolCallsList: ToolCall[] = []; // Maintain order
		const decoder = new TextDecoder();
		let accumulatedContentForChunk = '';
		let buffer = ''; // Buffer for incomplete JSON lines

		try {
			while (true) {
				const {done, value} = await reader.read();
				if (done) break;

				const chunk = decoder.decode(value);
				buffer += chunk;

				// Split on newlines but keep the last incomplete line in buffer
				const lines = buffer.split('\n');
				buffer = lines.pop() || ''; // Keep the last potentially incomplete line

				for (const line of lines) {
					if (line.startsWith('data: ')) {
						const dataStr = line.slice(6).trim();
						if (dataStr === '[DONE]') continue;
						if (!dataStr) continue; // Skip empty data lines

						try {
							let parsed: any;
							try {
								parsed = JSON.parse(dataStr);
							} catch (e) {
								console.error('Failed to parse streaming response line:', dataStr, e);
								continue;
							}
							if (!generationId && parsed.id) {
								generationId = parsed.id;
							}

							const contentDelta = parsed.choices[0]?.delta?.content || '';
							accumulatedContentForChunk += contentDelta;
							fullResponse += contentDelta;

							const deltaToolCalls = parsed.choices[0]?.delta?.tool_calls;
							if (deltaToolCalls) {
								for (const tcDelta of deltaToolCalls) {
									const id = tcDelta.id;
									const type = tcDelta.type || 'function';

									if (id) {
										if (!toolCallAggregator[id]) {
											toolCallAggregator[id] = {
												id: id,
												type: type as 'function',
												function: {
													name: tcDelta.function?.name || '',
													arguments: tcDelta.function?.arguments || ''
												}
											};
											aggregatedToolCallsList.push(toolCallAggregator[id]);
										} else {
											if (tcDelta.function?.name) {
												toolCallAggregator[id].function.name = tcDelta.function.name;
											}
											if (tcDelta.function?.arguments) {
												toolCallAggregator[id].function.arguments += tcDelta.function.arguments;
											}
										}
									} else if (aggregatedToolCallsList.length > 0) {
										// No ID in this delta, assume it's for the last tool call
										const lastToolCall = aggregatedToolCallsList[aggregatedToolCallsList.length - 1];
										if (tcDelta.function?.name && !lastToolCall.function.name) {
											lastToolCall.function.name = tcDelta.function.name;
										}
										if (tcDelta.function?.arguments) {
											lastToolCall.function.arguments += tcDelta.function.arguments;
										}
									}
								}
							}
						} catch (e) {
							console.error('Error parsing streaming response line:', dataStr, e);
						}
					}
				}
				// After processing all lines in a chunk, if there's content, send it
				if (accumulatedContentForChunk && onChunk) {
					await onChunk({
						content: accumulatedContentForChunk,
						done: false
					});
					accumulatedContentForChunk = ''; // Reset for next chunk
				}
			}
		} finally {
			reader.releaseLock();
		}

		// Ensure any final accumulated content is sent if loop exited abruptly
		if (accumulatedContentForChunk && onChunk) {
			await onChunk({
				content: accumulatedContentForChunk,
				done: false // Or true if we know it's the end
			});
		}

		const finalAssembledToolCalls = aggregatedToolCallsList.filter(tc => tc.id && (tc.function.name || tc.function.arguments));

		const result = {
			content: fullResponse,
			provider: 'openrouter',
			model: 'pending', // Will be updated by fetchGenerationDetails
			toolCalls: finalAssembledToolCalls
		};

		if (generationId && onChunk) {
			this.fetchGenerationDetails(generationId)
				.then(details => {
					onChunk({
						content: '', // Final chunk, only metadata and tools
						done: true,
						metadata: {
							system: this.systemId,
							...details
						},
						toolCalls: finalAssembledToolCalls
					}).catch(console.error);
				})
				.catch(console.error);
		} else if (onChunk) { // If no generationId but streaming, still need to mark as done
			onChunk({
				content: '',
				done: true,
				toolCalls: finalAssembledToolCalls // Send whatever was assembled
			}).catch(console.error);
		}
		return result;
	}

	protected async executeToolFunction(name: string, args: any): Promise<any> {
		// OpenRouter doesn't execute tools directly - it just returns tool calls
		// Tool execution is handled by BaseRouterTarget, which gets the tools from params
		throw new Error('OpenRouter does not execute tools directly');
	}
}
