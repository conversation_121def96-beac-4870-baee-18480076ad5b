{"$schema": "https://opencode.ai/config.json", "provider": {"lmstudio": {"npm": "@ai-sdk/openai-compatible", "name": "LMStudio", "options": {"baseURL": "http://192.168.4.50:1234/v1"}, "models": {"llama-3.2-3b": {"name": "Llama 3.2 3B"}, "qwen2.5-coder": {"name": "Qwen2.5 Coder"}, "unsloth/devstral-small-2505": {"name": "Devstral Small"}}}}, "experimental": {"hook": {"file_edited": {".json": [{"command": ["bun", "run", "prettier", "$FILE"]}]}, "session_completed": [{"command": ["touch", "./node_modules/foo"]}]}}}