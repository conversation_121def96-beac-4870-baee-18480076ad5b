<template>
	<div class="border-b bg-white overflow-x-auto w-full">
	  <div class="flex flex-wrap px-4 py-2 gap-2">
		<template v-for="taskStep in sortedTaskSteps" :key="taskStep.id">
		  <button
			v-if="shouldShowTask(taskStep)"
			:class="[
			  'hover:bg-purple-50 flex items-center gap-2 rounded-xl',
			  'px-3 py-2',
			  {'bg-purple-50': selectedTaskId === taskStep.id}
			]"
			@click="$emit('select-task', taskStep)">
			<div class="flex items-center">
			  <!-- Calendar icon for Schedule -->
			  <svg v-if="taskStep.taskTypeId === TASK_TYPES.SCHEDULE" class="w-4 h-4 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z" />
			  </svg>
			  <!-- People icon for Segment -->
			  <svg v-else-if="taskStep.taskTypeId === TASK_TYPES.SEGMENT" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
			  </svg>
			  <!-- Envelope icon for Content -->
			  <svg v-else-if="taskStep.taskTypeId === TASK_TYPES.CONTENT" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z" />
			  </svg>
			  <!-- Price tag icon for Promotion -->
			  <svg v-else-if="taskStep.taskTypeId === TASK_TYPES.PROMOTION" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z" />
				 </svg>
				 <!-- Image icon for Image Generation -->
				 <svg v-else-if="taskStep.taskTypeId === IMAGE_GENERATION" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
				 </svg>
				 <!-- Palette icon for Review Email Design -->
			  <svg v-else-if="taskStep.taskTypeId === TASK_TYPES.REVIEW_EMAIL_DESIGN" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67A2.5 2.5 0 0 1 12 22zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5a.54.54 0 0 0-.14-.35c-.41-.46-.63-1.05-.63-1.65a2.5 2.5 0 0 1 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z" />
				<circle cx="6.5" cy="11.5" r="1.5" />
				<circle cx="9.5" cy="7.5" r="1.5" />
				<circle cx="14.5" cy="7.5" r="1.5" />
				<circle cx="17.5" cy="11.5" r="1.5" />
			  </svg>
			  <!-- Klaviyo icon for Klaviyo Campaign -->
			  <svg v-else-if="taskStep.taskTypeId === TASK_TYPES.KLAVIYO_CAMPAIGN" class="w-4 h-4 text-gray-400 fill-current"
				:class="selectedTaskId === taskStep.id ? 'text-purple-600' : 'text-slate-600'"
				viewBox="0 0 24 24">
				<path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 14h-2V9h-2V7h4v10zm4-6h-2v2h2v2h-2v2h-2v-6h4V7h-6v12h4V11z" />
			  </svg>
			</div>
			<div class="flex flex-col">
			  <div class="flex items-center">
				<span class="font-medium text-sm" :class="{'text-purple-800': selectedTaskId === taskStep.id}">
				  {{ taskStep.name }}
				</span>
			  </div>
			</div>
		  </button>
		</template>
	  </div>
	</div>
  </template>

  <script>
  import { TaskType } from '../../constants/TaskTypes';

  export default {
 name: 'TaskSidebar',
 props: {
   taskSteps: {
  type: Array,
  default: () => []
   },
   selectedTaskId: {
  type: [String, Number],
  default: null
   },
   task: {
  type: Object,
  default: () => ({})
   }
 },
 emits: ['select-task'],
 data() {
   return {
  TASK_TYPES: TaskType, // Make TaskType accessible in template
  // Custom task type for Image Generation
  IMAGE_GENERATION: 'IMAGE_GENERATION',
  // Virtual task step for Image Generation that will always be shown
  imageGenerationTask: {
    id: 'image-generation',
    name: 'Image Generation',
    description: 'Generate images for your campaign',
    taskTypeId: 'IMAGE_GENERATION',
    position: -1 // Ensure it's positioned early, but we'll handle exact positioning in sortedTaskSteps
  }
   };
 },
	computed: {
	  visibleTasksCount() {
		// Include our custom Image Generation task in the count
		return this.taskSteps.filter(taskStep => this.shouldShowTask(taskStep)).length + 1;
	  },
	  sortedTaskSteps() {
		// Sort tasks by position (lowest to highest)
		const sortedApiTasks = [...this.taskSteps].sort((a, b) => {
		  // Handle missing position values
		  const posA = a.position !== undefined ? a.position : Number.MAX_VALUE;
		  const posB = b.position !== undefined ? b.position : Number.MAX_VALUE;
		  return posA - posB;
		});

		// If there are no tasks, just return our Image Generation task
		if (sortedApiTasks.length === 0) {
		  return [this.imageGenerationTask];
		}

		// Insert Image Generation as the second item
		// Take the first item, add our Image Generation task, then add the rest
		return [
		  sortedApiTasks[0],
		  this.imageGenerationTask,
		  ...sortedApiTasks.slice(1)
		];
	  }
	},
	methods: {
	  shouldShowTask(taskStep) {
		// Handle null/undefined taskStep
		if (!taskStep) {
		  return false;
		}

		// Always show our custom Image Generation task
		if (taskStep.taskTypeId === this.IMAGE_GENERATION) {
		  return true;
		}

		// Special handling for promotion tasks
		if (taskStep.taskTypeId === this.TASK_TYPES.PROMOTION) {
		  // Check for actual non-empty string values, not just truthy values
		  const promotionType = this.task?.campaign?.promotionType;
		  const promotionTitle = this.task?.campaign?.promotionTitle;

		  console.log('DEBUG - Promotion Task:', {
			taskId: taskStep.id,
			taskName: taskStep.name,
			promotionType,
			promotionTypeType: typeof promotionType,
			promotionTitle,
			promotionTitleType: typeof promotionTitle
		  });

		  // Only show promotion task when it has actual content in the fields
		  // Exclude strings that contain 'null' or are empty
		  const shouldShow = !!(this.task &&
					this.task.campaign &&
					(
					  (typeof promotionType === 'string' && promotionType.trim() !== '' && promotionType !== 'null') ||
					  (typeof promotionTitle === 'string' && promotionTitle.trim() !== '' && promotionTitle !== 'null')
					));

		  console.log('Should show promotion task:', shouldShow);
		  return shouldShow;
		}

		// Show all other task types
		return true;
	  },
	  getTaskDescription(taskStep) {
	 if (!taskStep) return "";

	 switch (taskStep.taskTypeId) {
	   case this.IMAGE_GENERATION:
	  return "Create images for your campaign";
	   case this.TASK_TYPES.SCHEDULE:
			return this.task?.campaign?.scheduledDate
			  ? this.formatScheduledTime(this.task.campaign.scheduledDate)
			  : "Not Set";
		  case this.TASK_TYPES.SEGMENT:
			return this.task?.segment?.aggregates?.totalCount
			  ? `${this.task.segment.aggregates.totalCount.toLocaleString()} customers`
			  : this.task?.segment?.customers?.length
			    ? `${this.task.segment.customers.length.toLocaleString()} customers`
			    : "No customers yet";
		  case this.TASK_TYPES.CONTENT:
			return taskStep.description;
		  case this.TASK_TYPES.PROMOTION:
			const type = this.task?.campaign?.promotionType;
			const title = this.task?.campaign?.promotionTitle;
			if (type && title) {
			  return `${type} - ${title}`;
			} else if (type) {
			  return type;
			} else if (title) {
			  return title;
			}
			return taskStep.description;
		  case this.TASK_TYPES.REVIEW_EMAIL_DESIGN:
			return this.task?.emailHtml
			  ? "Email design approved"
			  : "Pending approval";
		  case this.TASK_TYPES.KLAVIYO_CAMPAIGN:
			const segmentSynced = this.task?.segment?.externalId;
			const emailApproved = this.task?.emailHtml;
			if (this.task?.klaviyoCampaignId) {
			  return "Campaign exported";
			} else if (emailApproved) {
			  return "Ready to export";
			} else {
			  return "Email design needed";
			}
			return "Setup needed";
		  default:
			return taskStep.description;
		}
	  },
	  formatScheduledTime(dateString) {
		if (!dateString) return "Not Set";

		try {
		  console.log('TaskSidebar formatScheduledTime input:', dateString);

		  // The most direct approach - avoid Date objects completely for YYYY-MM-DD strings
		  // This eliminates any possibility of timezone shifts
		  if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
		    // Extract year, month, day directly from string
		    const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

		    // Map month number to month name
		    const shortMonthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
		      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

		    // Format directly using the extracted components
		    const formatted = `${shortMonthNames[month-1]} ${day}, ${year}`;
		    console.log('TaskSidebar direct string formatting:', formatted);
		    return formatted;
		  }

		  // Fallback for non-standard date strings (should not happen with API data)
		  console.warn('Non-standard date format in TaskSidebar:', dateString);

		  // Try to extract date parts from other string formats
		  if (typeof dateString === 'string') {
		    // If it's an ISO string with time (YYYY-MM-DDTHH:MM:SS...)
		    if (dateString.includes('T')) {
		      const datePart = dateString.split('T')[0];
		      if (datePart.match(/^\d{4}-\d{2}-\d{2}$/)) {
		        return this.formatScheduledTime(datePart); // Recursively handle the date part
		      }
		    }

		    // Last resort - try standard parsing but with careful logging
		    const dateObj = new Date(dateString);
		    if (!isNaN(dateObj.getTime())) {
		      console.log('TaskSidebar fallback date parsing:', {
		        input: dateString,
		        parsed: dateObj.toISOString(),
		        year: dateObj.getFullYear(),
		        month: dateObj.getMonth() + 1,
		        day: dateObj.getDate()
		      });

		      // Format with explicit month mapping to avoid timezone issues
		      const shortMonthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
		        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
		      return `${shortMonthNames[dateObj.getMonth()]} ${dateObj.getDate()}, ${dateObj.getFullYear()}`;
		    }
		  }

		  return "Invalid Date";
		} catch (error) {
		  console.error('Error formatting date in TaskSidebar:', error);
		  return "Date Error";
		}
	  }
	},
	watch: {
	  task: {
		deep: true,
		immediate: true,
		handler(newTask, oldTask) {
		  console.log('TaskSidebar: task object changed', {
			scheduledDate: newTask?.campaign?.scheduledDate,
			oldDate: oldTask?.campaign?.scheduledDate
		  });
		}
	  }
	}
  }
  </script>
