<template>
	<div class="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
		<!-- Section header -->
		<div class="mb-8 flex">
			<h1 class="text-2xl md:text-3xl text-slate-800 font-bold">Shopify Integration Settings</h1>
		</div>

		<!-- Section content -->
		<div class="bg-white shadow-lg rounded-sm mb-8">
			<div class="p-6 space-y-6">
				<section class="mt-5">
					<label class="block text-sm font-medium mb-1">Store URL</label>
					<div class="flex flex-col space-y-2">
						<div class="flex">
							<input
								type="text"
								class="form-input w-full"
								v-model="storeUrl"
								:readonly="connected"
								:placeholder="connected ? 'Connected' : 'Enter your Shopify store URL (e.g., mystore.myshopify.com)'"
								@blur="validateUrl"
								@input="clearErrors"
							/>
						</div>
						<div v-if="urlError" class="text-red-600 text-sm">
							{{ urlError }}
						</div>
						<div v-if="!urlError && storeUrl && isValidUrl" class="text-green-600 text-sm">
							✓ Valid Shopify URL format
						</div>
					</div>
					<div class="mt-2 space-y-1">
						<span class="text-xs text-gray-600 block">Enter your Shopify store domain without https://</span>
						<span class="text-xs text-gray-600 block">Format: yourstore.myshopify.com or custom-domain.com</span>
						<span class="text-xs text-gray-600 block">This URL is used for the Shopify app installation and API connections</span>
					</div>
				</section>

				<!-- Connection Status -->
				<section v-if="connected" class="mt-5 p-4 bg-green-50 border border-green-200 rounded-lg">
					<div class="flex items-center">
						<svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
						</svg>
						<span class="text-green-800 font-medium">Connected to Shopify</span>
					</div>
					<p class="text-green-700 text-sm mt-1">Your store is successfully connected. To disconnect, uninstall the Raleon app from your Shopify admin panel.</p>
				</section>

		<!-- Connecting Status -->
				<section v-if="isConnecting" class="mt-5 p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<div class="flex items-center">
						<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
						<span class="text-blue-800 font-medium">Connecting to Shopify</span>
					</div>
					<p class="text-blue-700 text-sm mt-1">
						Please wait while we redirect you to Shopify for authorization.
					</p>
				</section>

				<!-- Warning about URL changes -->
				<section v-if="!connected && hasExistingUrl && storeUrl !== existingUrl" class="mt-5 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
					<div class="flex items-start">
						<svg class="w-5 h-5 text-yellow-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
						</svg>
						<div>
							<span class="text-yellow-800 font-medium">Store URL Changed</span>
							<p class="text-yellow-700 text-sm mt-1">
								You've changed your store URL from <code class="bg-yellow-100 px-1 rounded">{{ existingUrl }}</code> to <code class="bg-yellow-100 px-1 rounded">{{ storeUrl }}</code>.
								Make sure this is correct before connecting.
							</p>
						</div>
					</div>
				</section>

				<div class="flex justify-end mt-5">
					<button
						v-if="!connected"
						class="btn bg-indigo-500 hover:bg-indigo-600 text-white cursor-pointer"
						:class="{
							'cursor-not-allowed': !canConnect,
							'bg-gray-400': !canConnect
						}"
						:disabled="!canConnect"
						@click="connect"
					>
						{{ validatingUrl ? 'Validating...' : isConnecting ? 'Connecting...' : 'Connect' }}
					</button>
				</div>

				<!-- Help section -->
				<section class="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<h3 class="text-blue-800 font-medium mb-2">Need Help?</h3>
					<div class="text-blue-700 text-sm space-y-2">
						<p><strong>Finding your Shopify URL:</strong></p>
						<ul class="list-disc list-inside ml-4 space-y-1">
							<li>Log into your Shopify admin panel</li>
							<li>Your store URL is shown in your browser's address bar (e.g., yourstore.myshopify.com)</li>
							<li>For custom domains, use your primary domain without www</li>
						</ul>
						<p class="mt-3"><strong>Common URL formats:</strong></p>
						<ul class="list-disc list-inside ml-4 space-y-1">
							<li><code class="bg-blue-100 px-1 rounded">mystore.myshopify.com</code></li>
							<li><code class="bg-blue-100 px-1 rounded">shop.mydomain.com</code></li>
							<li><code class="bg-blue-100 px-1 rounded">mydomain.com</code></li>
						</ul>
					</div>
				</section>
			</div>
		</div>
	</div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import * as Utils from '../../../client-old/utils/Utils';
import * as OrgServices from '../../services/organization';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'ShopifyConnection',
	setup(props, context) {
		const storeUrl = ref('');
		const existingUrl = ref('');
		const hasExistingUrl = ref(false);
		const validatingUrl = ref(false);
		const connected = ref(false);
		const urlError = ref('');
		const isValidUrl = ref(false);
		const isConnecting = ref(false);

		onMounted(async () => {
			// Load the existing external domain from organization record (same as AgentKnowledge)
			try {
				const response = await fetch(`${URL_DOMAIN}/organization/current`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok) {
					const currentOrg = await response.json();
					if (currentOrg.externalDomain) {
						// Parse domain for display (remove https:// if present)
						const displayDomain = currentOrg.externalDomain.replace(/^https?:\/\//, '');
						existingUrl.value = displayDomain;
						storeUrl.value = displayDomain;
						hasExistingUrl.value = true;
						validateUrl();
					}
				}
			} catch (error) {
				console.error('Error loading organization data:', error);
				// Fallback to localStorage if organization fetch fails
				const storedDomain = localStorage.getItem('externalDomain');
				if (storedDomain) {
					existingUrl.value = storedDomain;
					storeUrl.value = storedDomain;
					hasExistingUrl.value = true;
					validateUrl();
				}
			}

			// Check if already connected by checking for integration status
			await checkConnectionStatus();
		});

		function clearErrors() {
			urlError.value = '';
		}

		function validateUrl() {
			if (!storeUrl.value) {
				urlError.value = '';
				isValidUrl.value = false;
				return;
			}

			const url = storeUrl.value.trim();

			// Remove any protocol if accidentally included
			const cleanUrl = url.replace(/^https?:\/\//, '').replace(/\/$/, '');

			// Update the input value to the cleaned version
			storeUrl.value = cleanUrl;

			// Basic URL validation patterns
			const shopifyPattern = /^[a-zA-Z0-9\-]+\.myshopify\.com$/;
			const customDomainPattern = /^[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}$/;
			const subdomainPattern = /^[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}$/;

			if (shopifyPattern.test(cleanUrl) || customDomainPattern.test(cleanUrl) || subdomainPattern.test(cleanUrl)) {
				urlError.value = '';
				isValidUrl.value = true;
			} else {
				urlError.value = 'Please enter a valid store URL (e.g., mystore.myshopify.com or mydomain.com)';
				isValidUrl.value = false;
			}
		}

		async function checkConnectionStatus() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integrations`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok) {
					const integrations = await response.json();
					const shopifyIntegration = integrations.find(integration => integration.name === 'Shopify');
					connected.value = shopifyIntegration ? shopifyIntegration.connected : false;
				}
			} catch (error) {
				console.error('Error checking connection status:', error);
			}
		}

		async function connect() {
			if (!isValidUrl.value) {
				setStatus({
					type: 'fail',
					message: "Please enter a valid Shopify store URL before connecting.",
				});
				return;
			}

			validatingUrl.value = true;

			try {
				// Save the URL to organization settings first (this will update both the org record and localStorage)
				await saveStoreUrl();

				setStatus({
					type: 'success',
					message: 'Store URL saved successfully. Opening Shopify authorization...',
				});

				// Get the integration details to build the OAuth URL
				const response = await fetch(`${URL_DOMAIN}/integrations`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok) {
					const integrations = await response.json();
					const shopifyIntegration = integrations.find(integration => integration.name === 'Shopify');

					if (shopifyIntegration && shopifyIntegration.oAuthURL) {
						let url = shopifyIntegration.oAuthURL;
						const userJson = localStorage.getItem('userInfo');

						if (userJson) {
							const user = JSON.parse(userJson);
							if (user && user.id) {
								url = url.replace('{USERID}', user.id);
							}
						}
						url = url.replace('{STORE_URL}', storeUrl.value);
						url = url.replace('{ORGID}', localStorage.getItem('userOrgId'));						// Redirect to Shopify OAuth
						window.location.href = url;
					} else {
						setStatus({
							type: 'fail',
							message: "Unable to find Shopify integration configuration. Please try again or contact support.",
						});
					}
				} else {
					setStatus({
						type: 'fail',
						message: "Failed to load integration settings. Please try again.",
					});
				}
			} catch (error) {
				console.error('Error connecting to Shopify:', error);
				setStatus({
					type: 'fail',
					message: "Something went wrong connecting to Shopify. Please try again or contact support.",
				});
			} finally {
				validatingUrl.value = false;
			}
		}


		async function saveStoreUrl() {
			try {
				// Update localStorage immediately for other components to use
				localStorage.setItem('externalDomain', storeUrl.value);

				// Use the same pattern as AgentKnowledge.ts.vue
				const currentOrg = await OrgServices.getCurrentOrg();
				await OrgServices.patchOrgById(currentOrg.id, {
					externalDomain: storeUrl.value
				});

				existingUrl.value = storeUrl.value;
				hasExistingUrl.value = true;

			} catch (error) {
				console.error('Error saving store URL:', error);
				throw error;
			}
		}

		function setStatus({ type, message }) {
			context.emit('setStatus', { type, message });
		}

		const canConnect = computed(() => {
			return !validatingUrl.value && !isConnecting.value && isValidUrl.value && !connected.value;
		});

		return {
			storeUrl,
			existingUrl,
			hasExistingUrl,
			connected,
			validatingUrl,
			urlError,
			isValidUrl,
			isConnecting,
			canConnect,
			connect,
			validateUrl,
			clearErrors,
			setStatus,
		};
	}
};
</script>

<style scoped>
.form-input {
	padding: 0.5rem;
	border: 1px solid #d1d5db;
	border-radius: 0.375rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
	border-color: #68d391;
	box-shadow: 0 0 0 1px #68d391;
	outline: none;
}

.form-input[readonly] {
	background-color: #f3f4f6;
	color: #9ca3af;
	border: 1px dashed #d1d5db;
	cursor: not-allowed;
}

.btn {
	padding: 0.5rem 1rem;
	border-radius: 0.375rem;
	font-weight: 600;
	transition: background-color 0.15s ease-in-out;
	border: none;
}

code {
	font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
	font-size: 0.875rem;
}
</style>
