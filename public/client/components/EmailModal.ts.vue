<template>
	<div class="fixed inset-0 z-50 bg-white">
	  <!-- Top Bar -->
	  <div class="bg-white border-b px-6 py-4">
		<div class="flex items-center justify-between">
		  <!-- Left section with campaign name and breadcrumb -->
		  <div class="flex items-center gap-2">
			<h1 class="text-xl font-semibold">{{ campaignName }}</h1>
			<span class="text-gray-400 mx-2">/</span>
			<span class="text-gray-600 text-base">Reviewing Generated Email</span>
		  </div>

		  <!-- Right section with buttons -->
		  <div class="flex items-center gap-3">
			<!-- Save Design button -->
			<button
			  v-if="isEditorReady && fontsLoaded && !isGeneratingEmail && hasEmailDesign"
			  class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2"
			  @click="saveDesign"
			  :class="{'animate-pulse': isSaving}"
			>
			  <svg v-if="isSaving" class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
				<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
				<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
			  </svg>
			  {{ isSaving ? 'Saving...' : 'Save Design' }}
			</button>

			<!-- Close button -->
			<button
			  @click="$emit('close')"
			  class="p-2 rounded-full hover:bg-gray-100"
			  aria-label="Close"
			>
			  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
				<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
			  </svg>
			</button>
		  </div>
		</div>
	  </div>
	  <div class="h-full" style="height: calc(100% - 72px);">
		<!-- Loading state or Generate button if no design -->
		<div v-if="!isEditorReady || !fontsLoaded || isGeneratingEmail" class="h-full flex items-center justify-center">
		  <!-- Show loading animation while generating -->
		  <div v-if="isGeneratingEmail" class="text-center">
			<div class="bg-gray-50 border rounded-lg p-6 w-96 shadow-lg">
			  <div class="animate-bounce mb-4">
				<svg class="w-8 h-8 mx-auto text-purple-600" viewBox="0 0 24 24">
				  <path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
				</svg>
			  </div>
			  <div class="h-20 overflow-hidden">
				<transition-group
				  name="fade"
				  tag="div"
				  class="text-gray-700"
				>
				  <div v-for="(message, index) in currentGenerationMessages"
					   :key="message"
					   class="py-1"
					   :class="{'font-medium text-purple-700': index === currentMessageIndex}">
					{{ message }}
				  </div>
				</transition-group>
			  </div>
			  <div class="mt-4 text-sm text-gray-500">
				{{ showAnySecond ? 'Any second now...' : formatCountdown(countdownTime) + ' remaining' }}
			  </div>
			</div>
		  </div>
		  <!-- Show generate button if no email exists -->
		  <div v-else-if="!hasEmailDesign" class="text-center">
			<button
			  @click="$emit('generate')"
			  class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center gap-2"
			>
			  <svg class="w-5 h-5" viewBox="0 0 24 24">
				<path fill="currentColor" d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/>
			  </svg>
			  Generate Email
			</button>
			<p class="mt-3 text-sm text-gray-500">Generate a beautiful email based on your campaign brief</p>
		  </div>
		  <!-- Show loading spinner while editor initializes -->
		  <div v-else class="text-center">
			<svg class="w-8 h-8 mx-auto text-purple-600 animate-spin" viewBox="0 0 24 24">
			  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle>
			  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
			</svg>
			<p class="mt-3 text-sm text-gray-500">Loading email editor...</p>
		  </div>
		</div>

		<!-- Email Editor -->
		<div v-if="fontsLoaded && !isGeneratingEmail && hasEmailDesign" v-show="isEditorReady" class="h-full">
		  <EmailEditor
			:key="'email-editor-' + fontsLoaded + '-' + customFonts.length"
			:project-id="projectId"
			:tools="emailTools"
			:options="emailOptionsWithFont"
			ref="emailEditor"
			@load="onEditorLoaded"
			style="height: calc(100% - 64px); width: 100%;"
		  />
		</div>
	  </div>
	</div>
  </template>

  <script>
  import { EmailEditor } from 'vue-email-editor';
  import * as Utils from '../../client-old/utils/Utils';
  import * as OrganizationSettings from '../services/organization-settings';

  export default {
 name: "EmailModal",
 components: { EmailEditor },
 emits: ['close', 'editor-loaded', 'ready-for-design', 'save-design'],
 props: {
	  projectId: { type: String, required: true },
	  emailOptions: { type: Object, required: true },
	  emailTools: { type: Object, required: true },
	  hasEmailDesign: { type: Boolean, default: false },
	  isGeneratingEmail: { type: Boolean, default: false },
	  countdownTime: { type: Number, default: 120 },
	  showAnySecond: { type: Boolean, default: false },
	  currentGenerationMessages: { type: Array, default: () => [] },
	  currentMessageIndex: { type: Number, default: 0 },
	  campaignName: { type: String, required: true }
	},
	data() {
	  return {
		isEditorReady: false,
		designLoaded: false,
		isSaving: false,
		pendingDoneCallback: null,
		customFonts: [],
		fontsLoaded: false
	  };
	},
	computed: {
	  emailOptionsWithFont() {
		// Create enhanced email options with custom fonts
		const customFonts = [];
		
		// Add all custom fonts from the new structure
		this.customFonts.forEach(fontFamily => {
		  if (fontFamily.cssUrl && fontFamily.name) {
			const customFont = {
			  label: fontFamily.name,
			  value: fontFamily.cssValue || `'${fontFamily.name}', ${fontFamily.fallback || 'sans-serif'}`,
			  url: fontFamily.cssUrl,
			  weights: [
				{ label: 'Thin', value: 100 },
				{ label: 'Extra Light', value: 200 },
				{ label: 'Light', value: 300 },
				{ label: 'Regular', value: 400 },
				{ label: 'Medium', value: 500 },
				{ label: 'Semi Bold', value: 600 },
				{ label: 'Bold', value: 700 },
				{ label: 'Extra Bold', value: 800 },
				{ label: 'Black', value: 900 }
			  ]
			};
			customFonts.push(customFont);
		  }
		});
		
		console.log('EmailModal - Building font options, custom fonts count:', customFonts.length);
		
		const options = {
		  ...this.emailOptions,
		  displayMode: 'email',
		  fonts: {
			showDefaultFonts: true,
			customFonts: customFonts
		  }
		};
		
		console.log('EmailModal - Final font config:', {
		  showDefaultFonts: options.fonts.showDefaultFonts,
		  customFontsCount: options.fonts.customFonts.length,
		  customFonts: options.fonts.customFonts
		});
		
		return options;
	  }
	},
	methods: {
	  onEditorLoaded() {
		console.log('EmailModal onEditorLoaded called');
		console.log('EmailModal editor options at load time:', this.emailOptionsWithFont);
		this.isEditorReady = true;
		this.$emit('editor-loaded', this.$refs.emailEditor.editor);

		this.$refs.emailEditor.editor.addEventListener('image:uploaded', (data) => {
			const image = data.image;
			console.log('Image uploaded:', image);
			const { url, width, height, contentType } = image;

			// Optionally, save additional metadata or log the upload
			this.saveImageMetadata({ url, width, height, contentType });
		});

		this.$refs.emailEditor.editor.registerProvider('userUploads', (params, done) => {
			// Use the BrandImageManager to get the images
			const rootApp = this.$root;
			const brandImageManager = rootApp.$refs.brandImageManager;

			if (brandImageManager) {
				// If we have direct access to the BrandImageManager component
				console.log('Using BrandImageManager component for image provider');
				brandImageManager.getImagesForEmailEditor(params, done);
			} else {
				// If we don't have direct access, we'll use our own implementation
				console.log('Using fallback method for image provider');
				this.fetchImagesForEmailEditor(params, done);
			}
		})

		this.$refs.emailEditor.editor.registerCallback('image:removed', (data, done) => {
			console.log('Image removed:', data);

			// Get the image ID from the data
			const imageId = data.id;
			if (!imageId) {
				console.warn('No image ID provided for deletion');
				if (typeof done === 'function') done();
				return;
			}

			// Show confirmation before deleting
			this.confirmImageDeletion(imageId, done);
		});

		// If we have a design to load and editor is ready with fonts, notify parent
		if (this.hasEmailDesign && this.fontsLoaded && !this.designLoaded) {
		  	this.designLoaded = true;
		  	this.$emit('ready-for-design');


			//Immediately save the email design
			this.$refs.emailEditor.editor.exportHtml((data) => {
				this.$emit('save-design', data);

				// Set a timeout to reset the saving state after a brief delay to show feedback
				setTimeout(() => {
					this.isSaving = false;
				}, 1000);
			});
		}
	  },
	  async loadFontSettings() {
		console.log('EmailModal loadFontSettings started');
		try {
		  // Load custom fonts from organization settings
		  const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
		  console.log('EmailModal retrieved customFonts string:', customFontsString);
		  
		  if (customFontsString) {
			try {
			  this.customFonts = JSON.parse(customFontsString);
			  console.log('EmailModal parsed custom fonts:', this.customFonts);
			} catch (error) {
			  console.error('Error parsing custom fonts:', error);
			  this.customFonts = [];
			}
		  } else {
			console.log('EmailModal no custom fonts found in settings');
			this.customFonts = [];
		  }
		} catch (error) {
		  console.error('Error loading font settings for EmailModal:', error);
		  this.customFonts = [];
		} finally {
		  this.fontsLoaded = true;
		  console.log('EmailModal loadFontSettings completed, fontsLoaded:', this.fontsLoaded);
		  console.log('EmailModal final customFonts:', this.customFonts);
		}
	  },
	  async fetchImagesForEmailEditor(params, done) {
		try {
		  console.log('Email editor requested images with params:', params);
		  const page = params.page || 1;
		  const perPage = params.perPage || 20;

		  // Use Utils.URL_DOMAIN to ensure we use the same domain as BrandImageManager
		  const URL_DOMAIN = Utils.URL_DOMAIN;

		  const assetType = 'email'; // Default to email assets

		  const response = await fetch(`${URL_DOMAIN}/branding/images?assetType=${assetType}`, {
			headers: {
			  'Authorization': `Bearer ${localStorage.getItem('token')}`,
			},
		  });

		  if (!response.ok) {
			console.error('Failed to fetch images for email editor:', response.status);
			done([], { hasMore: false, page, perPage, total: 0 });
			return;
		  }

		  const allImages = await response.json();
		  console.log(`Fetched ${allImages.length} images for email editor`);

		  // Calculate pagination
		  const startIndex = (page - 1) * perPage;
		  const endIndex = startIndex + perPage;
		  const images = allImages.slice(startIndex, endIndex);
		  const hasMore = endIndex < allImages.length;
		  const total = allImages.length;

		  // Format images for email editor
		  const formattedImages = images.map(image => ({
			id: image.id,
			location: image.url, // url is the field in our API, but location is what email editor expects
			width: image.width || 0,
			height: image.height || 0,
			contentType: image.contentType || image.imageType || 'image/png', // Support both field names
			source: 'user',
			name: image.friendlyname // Additional field that might be useful
		  }));

		  console.log(`Returning ${formattedImages.length} formatted images to email editor`);
		  done(formattedImages, { hasMore, page, perPage, total });
		} catch (error) {
		  console.error('Error fetching images for email editor:', error);
		  done([], { hasMore: false, page: params.page || 1, perPage: params.perPage || 20, total: 0 });
		}
	  },
	  async deleteImage(id) {
		try {
			console.log('Deleting image with ID:', id);

			// Try to use the BrandImageManager component if available
			const rootApp = this.$root;
			const brandImageManager = rootApp.$refs.brandImageManager;

			if (brandImageManager) {
				// Use the BrandImageManager's deleteImage method
				console.log('Using BrandImageManager to delete image');
				await brandImageManager.deleteImage(id, true);
			} else {
				// Fallback: implement our own delete logic
				console.log('Using fallback method to delete image');
				const URL_DOMAIN = Utils.URL_DOMAIN;

				const response = await fetch(`${URL_DOMAIN}/branding/images/${id}`, {
					method: 'DELETE',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
				});

				if (!response.ok) {
					throw new Error(`Failed to delete image: ${response.status} ${response.statusText}`);
				}

				console.log('Image deleted successfully');
			}
		} catch (error) {
			console.error('Error deleting image:', error);
			// Optionally show a notification to the user
		}
	  },
	  confirmImageDeletion(imageId, doneCallback) {
		// Try to use the BrandImageManager component for deletion confirmation
		const rootApp = this.$root;
		const brandImageManager = rootApp.$refs.brandImageManager;

		if (brandImageManager) {
			// Use BrandImageManager's confirmation modal
			console.log('Using BrandImageManager for deletion confirmation');
			// Store the callback to call after deletion
			this.pendingDoneCallback = doneCallback;
			// Set up a one-time event listener for when deletion is complete
			const handleDeletion = () => {
				if (typeof this.pendingDoneCallback === 'function') {
					this.pendingDoneCallback();
					this.pendingDoneCallback = null;
				}
				brandImageManager.$off('deletion-complete', handleDeletion);
			};
			brandImageManager.$on('deletion-complete', handleDeletion);

			// Trigger the confirmation modal
			brandImageManager.confirmDelete(imageId);
		} else {
			// Fallback to immediate deletion with simple confirmation
			if (confirm('Are you sure you want to delete this image?')) {
				this.deleteImage(imageId);
			}

			// Call the done callback
			if (typeof doneCallback === 'function') {
				doneCallback();
			}
		}
	  },
	  async saveImageMetadata(imageData) {
		try {
		  console.log("Saving image metadata:", imageData);

		  // Call the API endpoint to save image metadata
		  const URL_DOMAIN = Utils.URL_DOMAIN;
		  const response = await fetch(`${URL_DOMAIN}/branding/images/metadata`, {
		  	method: 'POST',
		  	headers: {
		  	'Content-Type': 'application/json',
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
		  	},
				body: JSON.stringify({
					url: imageData.url,
					width: imageData.width,
					height: imageData.height,
					contentType: imageData.contentType,
					assetType: 'email',
					friendlyname: imageData.filename || undefined
		  	})
		  });

		  if (!response.ok) {
		  	throw new Error(`Failed to save image metadata: ${response.statusText}`);
		  }

		  const savedImage = await response.json();
		  console.log('Image metadata saved successfully:', savedImage);
		  return savedImage;
		} catch (error) {
		  console.error('Error saving image metadata:', error);
		  // You could add error handling UI here if needed
		}
	  },
	  formatCountdown(seconds) {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
	  },
	  saveDesign() {
		if (!this.$refs.emailEditor?.editor) return;

		this.isSaving = true;

		this.$refs.emailEditor.editor.exportHtml((data) => {
			this.$emit('save-design', data);

			// Set a timeout to reset the saving state after a brief delay to show feedback
			setTimeout(() => {
				this.isSaving = false;
			}, 1000);
		});

		// Use saveDesign to get the JSON directly
		this.$refs.emailEditor.editor.saveDesign((design) => {
		  this.$emit('save-design', design);

		  // Set a timeout to reset the saving state after a brief delay to show feedback
		  setTimeout(() => {
		    this.isSaving = false;
		  }, 1000);
		});
	  }
	},
	async mounted() {
	  console.log('EmailModal mounted - starting font loading');
	  // Preload font settings to ensure they're available when the editor loads
	  await this.loadFontSettings();
	  console.log('EmailModal mounted - font loading complete');
	},
	watch: {
	  hasEmailDesign(newVal) {
		// If we now have a design and editor is already loaded with fonts
		if (newVal && this.isEditorReady && this.fontsLoaded && !this.designLoaded) {
		  this.designLoaded = true;
		  this.$emit('ready-for-design');
		}
	  }
	}
  };
  </script>

  <style scoped>
  .fade-enter-active, .fade-leave-active {
	transition: all 0.5s ease;
  }
  .fade-enter-from, .fade-leave-to {
	opacity: 0;
	transform: translateY(20px);
  }
  .fade-enter-to, .fade-leave-from {
	opacity: 1;
	transform: translateY(0);
  }
  </style>
