<template>
  <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
    <!-- Calendar navigation header -->
    <div class="bg-white px-4 py-3 flex items-center justify-between border-b border-gray-200">
      <div class="flex items-center">
        <h2 class="text-lg font-semibold text-gray-900">{{ calendarTitle }}</h2>
      </div>
      <div class="flex items-center space-x-2">
        <button
          @click="previousMonth"
          class="p-2 rounded-full hover:bg-gray-100 text-gray-600 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
        <button
          @click="$emit('go-to-current')"
          class="px-3 py-1 text-sm font-medium rounded bg-purple-100 text-purple-700 hover:bg-purple-200 focus:outline-none"
        >
          Today
        </button>
        <button
          @click="nextMonth"
          class="p-2 rounded-full hover:bg-gray-100 text-gray-600 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="bg-white">
      <!-- Days of week header -->
      <div class="grid grid-cols-7 gap-px bg-gray-200 text-sm font-semibold text-center text-gray-700">
        <div v-for="day in daysOfWeek" :key="day" class="py-2 bg-white">
          {{ day }}
        </div>
      </div>

      <!-- Calendar days -->
      <div class="grid grid-cols-7 gap-px bg-gray-200">
        <div
          v-for="day in calendarDays"
          :key="day.date"
          class="h-36 bg-white flex flex-col overflow-hidden"
          :class="{
            'bg-purple-50': day.isToday,
            'text-gray-400': !day.isCurrentMonth
          }"
        >
          <!-- Day number -->
          <div class="p-1 flex justify-between">
            <span
              class="text-sm font-medium h-6 w-6 flex items-center justify-center rounded-full"
              :class="{ 'bg-purple-600 text-white': day.isToday }"
            >
              {{ formatDayNumber(day.date) }}
            </span>
            <span v-if="day.totalEmails > 0" class="text-xs text-purple-600 font-medium">
              {{ day.totalEmails }} {{ day.totalEmails === 1 ? 'email' : 'emails' }}
            </span>
          </div>

          <!-- Emails for this day -->
          <div class="p-1 space-y-1 flex-1 flex flex-col">
            <div
              v-for="(email, idx) in day.emails.slice(0, 4)"
              :key="idx"
              @click="$emit('navigate', email.id)"
              class="px-2 py-1 text-xs rounded bg-white border border-gray-200 hover:border-purple-300 hover:bg-purple-50 cursor-pointer"
              :class="getStatusColorForCalendar(email.status)"
            >
              <div class="flex flex-col">
                <div class="truncate font-medium">{{ email.title }}</div>
                <div class="flex justify-between items-center mt-0.5">
                  <span class="px-1.5 py-0.5 rounded-full text-[9px] font-medium" :class="getStatusTextColor(email.status)">
                    {{ getDisplayStatus(email.status) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Spacer to push the "more" indicator to the bottom when needed -->
            <div class="flex-grow" v-if="day.emails.length > 4"></div>

            <!-- "More" indicator if needed -->
            <div
              v-if="day.emails.length > 4"
              class="px-2 py-1 text-xs text-center text-purple-700 bg-purple-50 rounded hover:bg-purple-100 cursor-pointer mt-1"
              @click="$emit('show-day-detail', day)"
            >
              + {{ day.emails.length - 4 }} more
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailCalendarView',

  props: {
    currentMonth: {
      type: Number,
      required: true
    },
    currentYear: {
      type: Number,
      required: true
    },
    calendarDays: {
      type: Array,
      required: true
    },
    daysOfWeek: {
      type: Array,
      default: () => ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    }
  },

  computed: {
    calendarTitle() {
      return new Date(this.currentYear, this.currentMonth).toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    }
  },

  methods: {
    previousMonth() {
      this.$emit('previous-month');
    },

    nextMonth() {
      this.$emit('next-month');
    },

    getStatusColorForCalendar(status) {
      if (status === 'Processing') return 'border-purple-300 bg-purple-50';
      const colors = {
        'Ready': 'border-green-300 bg-green-50',
        'In Progress': 'border-blue-300 bg-blue-50',
        'In Review': 'border-yellow-300 bg-yellow-50',
        'Not Started': 'border-gray-300',
        'Complete': 'border-purple-300 bg-purple-50'
      };
      return colors[status] || 'border-gray-300';
    },

    getStatusTextColor(status) {
      if (status === 'Processing') return 'bg-purple-100 text-purple-800';
      const colors = {
        'Ready': 'bg-green-100 text-green-800',
        'In Progress': 'bg-blue-100 text-blue-800',
        'In Review': 'bg-yellow-100 text-yellow-800',
        'Not Started': 'bg-gray-100 text-gray-800',
        'Complete': 'bg-purple-100 text-purple-800'
      };
      return colors[status] || 'bg-gray-100 text-gray-800';
    },

    getDisplayStatus(status) {
      const statusMap = {
        'Ready': 'Not Started',
        'In Progress': 'In Progress',
        'In Review': 'In Review',
        'Not Started': 'Not Started',
        'Complete': 'Complete',
        'Processing': 'Processing'
      };
      return statusMap[status] || status;
    },

    formatDayNumber(date) {
      // Create a date object from the UTC string
      const formattedDate = new Date(date);

      // Use the UTC date methods to get the correct day number
      return formattedDate.getUTCDate();
    }
  },

  emits: ['previous-month', 'next-month', 'go-to-current', 'navigate', 'show-day-detail']
}
</script>
