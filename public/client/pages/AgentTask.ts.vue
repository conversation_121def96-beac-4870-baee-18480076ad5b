<template>
	<div class="h-screen overflow-hidden bg-[#F5F5F5]">
		<TopBar :campaign="task?.campaign" :status="task?.status" :plan="task?.plan"
			:klaviyo-campaign-id="task?.klaviyoCampaignId" :task="task" @back="handleBackNavigation"
			@create-klaviyo="createKlaviyoCampaign" @launch-klaviyo="launchKlaviyoCampaign"
			@mark-complete="markTaskComplete" :is-creating-klaviyo-campaign="isCreatingKlaviyoCampaign"
			:klaviyo-status-message="klaviyoStatusMessage" :should-show-klaviyo-button="selectedTask?.taskTypeId === TASK_TYPES.KLAVIYO_CAMPAIGN"
			@update-campaign-name="updateCampaignName" @update-campaign-description="updateCampaignDescription" @update-status="updateTaskStatus" />

		<!-- Main Content Area -->
		<div class="flex flex-col h-[calc(100vh-73px)] overflow-hidden">
			<!-- Top Row - Tasks -->
			<TaskSidebar :task-steps="taskSteps" :selected-task-id="selectedTask?.id" :task="task"
				@select-task="selectedTask = $event" />

			<!-- Content View -->
			<div class="flex-1 overflow-hidden relative">
				<transition enter-active-class="transition-opacity duration-300 ease-out"
					leave-active-class="transition-opacity duration-200 ease-in" enter-from-class="opacity-0"
					leave-to-class="opacity-0" mode="out-in">
					<!-- Content Area Based on taskTypeId -->
					<div v-if="selectedTask" class="h-full overflow-hidden" :key="selectedTask.id">
						<ContentTask v-if="selectedTask.taskTypeId === TASK_TYPES.CONTENT" ref="contentTaskRef"
							:content-sections="contentSections" :subject-line="subjectLineRef" :preview-line="previewLineRef"
							:is-generating-subject="isGeneratingSubject" :is-generating-preview="isGeneratingPreview"
							:is-generating="isGenerating" :is-generating-email="isGeneratingEmail"
							:countdown-time="countdownTime" :show-any-second="showAnySecond"
							:current-generation-messages="currentGenerationMessages"
							:current-message-index="currentMessageIndex" :email-design="task?.emailDesign"
							:campaign-id="task?.campaign?.id"
							:task-conversation-id="task?.conversationId || null"
							@show-preview="showEmailPreview" @save-design="saveDesign"
							@generate-email="generateNewEmail" @export-html="exportHtml" @open-regenerate-modal="openRegenerateModal"
							@update:subject-line="updateSubjectLine" @update:preview-line="updatePreviewLine"
							@update:content="handleContentUpdate"
							@show-email-modal="showEmailModal = true" />
						<SegmentTask v-else-if="selectedTask.taskTypeId === TASK_TYPES.SEGMENT"
							:segment="task?.segment"
							:task-data="selectedTask.data"
							:campaign-id="task?.campaign?.id"
							@update:segment="updateSegment" />
						<PromotionTask
							v-else-if="selectedTask?.taskTypeId === TASK_TYPES.PROMOTION &&
									  task?.campaign &&
									  ((typeof task.campaign.promotionType === 'string' && task.campaign.promotionType.trim() !== '' && task.campaign.promotionType !== 'null') ||
									   (typeof task.campaign.promotionTitle === 'string' && task.campaign.promotionTitle.trim() !== '' && task.campaign.promotionTitle !== 'null'))"
							:task="task" />
						<ScheduleTask
							v-else-if="selectedTask.taskTypeId === TASK_TYPES.SCHEDULE"
							:task="task"
							@task-updated="handleTaskUpdated"
							@error="handleScheduleError"
						/>
						<ReviewEmailDesignTask
							v-else-if="selectedTask.taskTypeId === TASK_TYPES.REVIEW_EMAIL_DESIGN"
							:task="task"
							:is-generating-email="isGeneratingEmail"
							:subject-line="subjectLineRef"
							:preview-line="previewLineRef"
							@generate-email="generateNewEmail"
							@regenerate-email="regenerateEmail"
							@show-email-modal="showEmailModal = true" />
						<KlaviyoCampaignTask
							v-else-if="selectedTask.taskTypeId === TASK_TYPES.KLAVIYO_CAMPAIGN"
							:task="task"
							:segment="task?.segment"
							:task-steps="taskSteps"
							:is-creating-klaviyo-campaign="isCreatingKlaviyoCampaign"
							:klaviyoStatusMessage="klaviyoStatusMessage"
							:klaviyo-status="{
								status: isCreatingKlaviyoCampaign ? 'processing' : (task?.klaviyoCampaignId ? 'completed' : null),
								step: klaviyoStep,
								startTime: klaviyoStep ? new Date().toISOString() : null
							}"
							@select-task="selectedTask = $event"
							@export-to-klaviyo="createKlaviyoCampaign"
							@export-to-klaviyo-html-only="createKlaviyoCampaignHtmlOnly"
							@resync-klaviyo="resyncKlaviyoCampaign"
							@resync-klaviyo-html-only="resyncKlaviyoCampaignHtmlOnly" />
						<ImageGenerationTask
							v-else-if="selectedTask.taskTypeId === 'IMAGE_GENERATION'"
							:task-id="taskId || undefined"
							:campaign-id="task?.campaign?.id"
							@use-in-email="handleUseImageInEmail" />
						<EmailJobsView
							v-else-if="selectedTask.taskTypeId === 'EMAIL_JOBS'"
							:task="task"
							:project-id="projectId"
							@task-updated="handleEmailJobTaskUpdated" />
						<!-- Add other task type components here -->
					</div>
				</transition>
			</div>
		</div>

		<!-- Email Modal -->
		<EmailModal v-if="showEmailModal" :project-id="projectId" :email-tools="emailTools"
			:email-options="emailOptions" :has-email-design="!!task?.emailDesign"
			:is-generating-email="isGeneratingEmail" :countdown-time="countdownTime" :show-any-second="showAnySecond"
			:current-generation-messages="currentGenerationMessages" :current-message-index="currentMessageIndex"
			:campaign-name="task?.campaign?.name || 'Email Campaign'" @close="showEmailModal = false"
			@editor-loaded="onEditorLoaded" @generate="generateNewEmail" @save-design="saveDesign"
			@ready-for-design="loadPendingDesign" />

		<!-- Regenerate Brief Modal -->
		<RegenerateBriefModal
			 :show="showRegenerateModal"
			 :is-generating="isGenerating"
			 @close="showRegenerateModal = false"
			 @submit="handleRegenerateSubmit"
		/>

		<!-- Toast Notification -->
		<Teleport to="body">
			<div v-if="toast.show" class="fixed bottom-5 right-5 z-50 min-w-[300px]">
				<div class="flex items-center p-4 text-sm text-white rounded-lg shadow-md"
					:class="{
						'bg-emerald-500': toast.type === 'success',
						'bg-blue-500': toast.type === 'info',
						'bg-amber-500': toast.type === 'warning',
						'bg-rose-500': toast.type === 'error'
					}">
					<div class="flex-1">{{ toast.message }}</div>
					<button @click="closeToast" class="ml-4 text-white hover:text-white/80">
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
							<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
						</svg>
					</button>
				</div>
			</div>
		</Teleport>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from '@vue/runtime-core';
import { useRoute, useRouter } from 'vue-router';
import TopBar from '../components/agent-task/TopBar.ts.vue';
import TaskSidebar from '../components/agent-task/TaskSidebar.ts.vue';
import ContentTask from '../components/agent-task/ContentTask.ts.vue';
import SegmentTask from '../components/agent-task/SegmentTask.ts.vue';
import EmailModal from '../components/EmailModal.ts.vue';
import PromotionTask from '../components/agent-task/PromotionTask.ts.vue';
import ScheduleTask from '../components/agent-task/ScheduleTask.ts.vue';
import ReviewEmailDesignTask from '../components/agent-task/ReviewEmailDesignTask.ts.vue';
import KlaviyoCampaignTask from '../components/agent-task/KlaviyoCampaignTask.ts.vue';
import ImageGenerationTask from '../components/agent-task/ImageGenerationTask.ts.vue';
import EmailJobsView from '../components/agent-task/EmailJobsView.vue';
import RegenerateBriefModal from '../components/agent-task/RegenerateBriefModal.vue';
import { URL_DOMAIN } from '../utils/utils';
import { customerIOTrackEvent } from '../services/customerio.js';
import { TaskType } from '../constants/TaskTypes';

// --- Route and Router ---
const route = useRoute();
const router = useRouter();

// --- Component Refs ---
const contentTaskRef = ref<InstanceType<typeof ContentTask> | null>(null); // Ref for ContentTask component instance

// --- Constants ---
const TASK_TYPES = TaskType;
// Add custom task type for Image Generation
const IMAGE_GENERATION = 'IMAGE_GENERATION';
// Add custom task type for Email Jobs
const EMAIL_JOBS = 'EMAIL_JOBS';
const projectId = '267562'; // Consider making this dynamic if needed

// --- Reactive State ---
const taskId = ref<string | null>(null);
const task = ref<any>(null); // Consider defining a proper Task interface
const taskSteps = ref<any[]>([]); // Consider defining a TaskStep interface
const selectedTask = ref<any>(null); // Consider defining a TaskStep interface
const subjectLineRef = ref(''); // Use ref for explicit reactivity
const previewLineRef = ref(''); // Use ref for explicit reactivity
const isGeneratingSubject = ref(false); // Keep these if needed for UI elsewhere
const isGeneratingPreview = ref(false); // Keep these if needed for UI elsewhere
const isGenerating = ref(false);
const isGeneratingEmail = ref(false);
const isCreatingKlaviyoCampaign = ref(false);
const klaviyoStatusMessage = ref('Creating...');
const klaviyoStep = ref('');
const countdownTime = ref(120);
const countdownInterval = ref<NodeJS.Timeout | null>(null);
const showAnySecond = ref(false);
const pollingInterval = ref<NodeJS.Timeout | null>(null);
const showRegenerateModal = ref(false);
const klaviyoPollingInterval = ref<NodeJS.Timeout | null>(null);
const isInitializing = ref(true);
const showEmailModal = ref(false);
const emailEditor = ref<any>(null); // Type for Unlayer editor instance
const emailDesign = ref<any>(null); // Store email design locally if needed

const generationMessages = [
	"Analyzing your campaign brief...",
	"Examining store performance data...",
	"Crafting compelling subject lines...",
	"Designing layout and structure...",
	"Optimizing for mobile devices...",
	"Adding brand elements...",
	"Incorporating promotional content...",
	"Fine-tuning design elements...",
	"Applying best practices...",
	"Finalizing your email..."
];
const currentMessageIndex = ref(0);
const currentGenerationMessages = ref<string[]>([]);
const messageInterval = ref<NodeJS.Timeout | null>(null);

// --- Email Editor Config ---
const emailOptions = {
	appearance: { panels: { tools: { dock: 'left' } } },
	features: { userUploads: { enabled: true, search: true } },
	user: { id: localStorage.getItem('userOrgId') },
	displayMode: 'email'
};
const emailTools = { image: { enabled: true }, carousel: { enabled: false }, form: { enabled: false }, menu: { enabled: false } };

// --- Computed Properties ---
const contentSections = computed(() => {
	return taskSteps.value.filter((step) => step.taskTypeId === TASK_TYPES.CONTENT);
});

const selectedTaskData = computed(() => {
    // Helper computed to safely parse selectedTask data
    if (!selectedTask.value?.data) return null;
    try {
        const dataString = String(selectedTask.value.data);
        if (!dataString || dataString.trim() === '') return {};

        // Check if the data contains <brief> tags and extract the content
        let dataToProcess = dataString;
        const briefTagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
        const match = dataToProcess.match(briefTagRegex);

        if (match && match[1]) {
            // Found content between <brief> tags, use that for parsing
            dataToProcess = match[1].trim();
        }

        // Use safeParseJson instead of JSON.parse
        return safeParseJson(dataToProcess);
    } catch (error) {
        console.error('Error parsing selectedTask data in computed:', error, selectedTask.value.data);
        if (typeof selectedTask.value.data === 'string') {
            return { briefText: selectedTask.value.data };
        }
        return {};
    }
});


// --- Watchers ---
watch(() => route.params.taskId, (newId) => {
	const currentId = Array.isArray(newId) ? newId[0] : newId; // Handle potential array
	if (currentId && currentId !== taskId.value) {
		taskId.value = currentId;
		fetchTask(currentId);
	}
}, { immediate: true }); // Run immediately to fetch on initial load

// Import the safeParseJson function from JsonUtils
import { safeParseJson, validateBrief } from '../utils/JsonUtils';

watch(selectedTask, (newTask) => {
    if (newTask?.data) {
        try {
            const dataString = String(newTask.data);
            if (dataString && dataString.trim() !== '') {
                // Check if the data contains <brief> tags and extract the content
                let dataToProcess = dataString;
                const briefTagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
                const match = dataToProcess.match(briefTagRegex);

                if (match && match[1]) {
                    // Found content between <brief> tags, use that for parsing
                    dataToProcess = match[1].trim();
                }

                // Use safeParseJson instead of JSON.parse
                const parsedData = safeParseJson(dataToProcess);

                // Validate the brief data to ensure it has the expected structure
                const validBrief = validateBrief(parsedData);

                subjectLineRef.value = validBrief.subjectLine;
                previewLineRef.value = validBrief.previewText;
            } else {
                 subjectLineRef.value = '';
                 previewLineRef.value = '';
            }
        } catch (error) {
            console.error('Error parsing selectedTask data in watcher:', error, newTask.data);
            // Reset refs if parsing fails
            subjectLineRef.value = '';
            previewLineRef.value = '';
        }
    } else {
        // Reset refs if no task or no data
        subjectLineRef.value = '';
        previewLineRef.value = '';
    }
}, { deep: true, immediate: true }); // Watch deeply and run immediately


// --- Methods ---
async function fetchTask(id: string) {
	const token = localStorage.getItem('token');
	if (!token) {
		console.error('No auth token found');
		router.push('/signin');
		return;
	}

	const url = `${URL_DOMAIN}/planner/task/${id}`;

	try {
		const response = await fetch(url, {
			method: 'GET',
			headers: {
				'Authorization': `Bearer ${token}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json',
				'ngrok-skip-browser-warning': 'true' // Note: ngrok header might not be needed in production
			}
		});


		if (!response.ok) {
			throw new Error(`Failed to fetch task: ${response.status}`);
		}

		const data = await response.json();

		task.value = data;
        emailDesign.value = data.emailDesign; // Store email design
		taskSteps.value = (data.taskSteps || []).sort((a: any, b: any) => a.position - b.position);

		// Log the task object to see all properties

		// If conversationId is not set, try to find it in the data
		if (!task.value.conversationId && data.conversationId) {
			task.value.conversationId = data.conversationId;
		}

		// Update selectedTask *after* taskSteps is updated
        // Ensure we find the task *within the newly fetched taskSteps*
        const currentSelectedId = selectedTask.value?.id;
        if (currentSelectedId) {
            const newSelectedTaskInstance = taskSteps.value.find(step => step.id === currentSelectedId);
            if (newSelectedTaskInstance) {
                selectedTask.value = newSelectedTaskInstance;
            } else if (taskSteps.value.length > 0) {
                 selectedTask.value = taskSteps.value[0]; // Fallback to first if previous selection disappears
            } else {
                 selectedTask.value = null; // No tasks left
            }
        } else if (taskSteps.value.length > 0) {
            selectedTask.value = taskSteps.value[0]; // Default to first task if none was selected
        } else {
             selectedTask.value = null;
        }


		// Update task status to 'Campaign Ready' if it's currently 'Ready'
		if (task.value.status === 'Ready') {
			await updateTaskStatus('Campaign Ready');
            // Re-fetch might be needed if status update changes other data, or update locally
            if (task.value) task.value.status = 'Campaign Ready';
		} else {
		}

		isInitializing.value = false;
	} catch (error) {
		console.error('Error fetching task:', error);
		// Check if error is an instance of Response and check status
        if (error instanceof Response && error.status === 401) {
             router.push('/signin');
        } else if (error instanceof Error && error.message.includes('401')) {
             // Handle cases where the error message might contain the status code
             router.push('/signin');
        }
	}
}

async function checkEmailGenerationStatus() {
	if (!taskId.value) return;

	try {
		const statusResponse = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/email-status`, {
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			}
		});

		if (statusResponse.ok) {
			const status = await statusResponse.json();
			if (status.status === 'pending') {
				isGeneratingEmail.value = true;
				currentMessageIndex.value = 0;
				currentGenerationMessages.value = [...generationMessages];
				startMessageAnimation();
				startCountdown();
				startEmailStatusPolling();
			}
		}
	} catch (error) {
		console.error('Error checking email generation status:', error);
	}
}

function startEmailStatusPolling() {
    if (pollingInterval.value) clearInterval(pollingInterval.value); // Clear existing interval

	pollingInterval.value = setInterval(async () => {
		if (!taskId.value) {
            clearInterval(pollingInterval.value!);
            return;
        }
		try {
			const statusResponse = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/email-status`, {
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				}
			});

			if (statusResponse.ok) {
				const status = await statusResponse.json();
				if (status.status === 'completed') {
					clearInterval(pollingInterval.value!);
					if (messageInterval.value) clearInterval(messageInterval.value);
					if (countdownInterval.value) clearInterval(countdownInterval.value);
					isGeneratingEmail.value = false;
					await fetchTask(taskId.value); // Refresh task
					customerIOTrackEvent('email_template_generated', {
						taskId: taskId.value,
						campaignName: task.value?.campaign?.name
					});
				} else if (status.status === 'failed') {
					clearInterval(pollingInterval.value!);
                    if (messageInterval.value) clearInterval(messageInterval.value);
                    if (countdownInterval.value) clearInterval(countdownInterval.value);
					isGeneratingEmail.value = false;
                    // Optionally show error message to user
				}
			} else {
                 // Handle non-ok response if needed (e.g., server error during polling)
                 console.error('Email status polling failed:', statusResponse.status);
                 clearInterval(pollingInterval.value!);
                 isGeneratingEmail.value = false; // Stop generation indicator on poll failure
            }
		} catch (error) {
			console.error('Error polling email status:', error);
            clearInterval(pollingInterval.value!); // Stop polling on error
            isGeneratingEmail.value = false;
		}
	}, 2000);
}

async function updateTaskStatus(status: string) {
	if (!taskId.value) return false;
	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ status: status })
		});

		if (!response.ok) {
			throw new Error(`Failed to update task status to ${status}`);
		}

		// Update local task status
		if (task.value) {
			task.value.status = status;
		}
		return true;
	} catch (error) {
		console.error(`Error updating task status to ${status}:`, error);
		return false;
	}
}

async function markTaskComplete() {
	const success = await updateTaskStatus('Complete');
	if (success && taskId.value) {
		await fetchTask(taskId.value); // Refresh task data
	}
}

function launchKlaviyoCampaign() {
	if (task.value?.klaviyoCampaignId) {
		window.open(`https://www.klaviyo.com/campaign/${task.value.klaviyoCampaignId}/wizard/1`, '_blank');
	}
}

async function createKlaviyoCampaign() {
	if (isGenerating.value || isCreatingKlaviyoCampaign.value || !taskId.value) return;

	isCreatingKlaviyoCampaign.value = true;
	klaviyoStatusMessage.value = 'Creating...';

	try {
		const htmlContent = task.value?.emailHTML || '';
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/create-klaviyo-campaign`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ html: htmlContent })
		});

		if (!response.ok) {
			throw new Error('Failed to create Klaviyo campaign');
		}

		await checkKlaviyoCampaignStatus(); // Check status immediately
		startKlaviyoStatusPolling(); // Start polling

		customerIOTrackEvent('klaviyo_campaign_creation_started', {
			taskId: taskId.value,
			campaignName: task.value?.campaign?.name
		});
	} catch (error) {
		console.error('Error creating Klaviyo campaign:', error);
		isCreatingKlaviyoCampaign.value = false;
		klaviyoStatusMessage.value = 'Failed';
		if (taskId.value) await fetchTask(taskId.value); // Refresh task on error
	}
}

async function resyncKlaviyoCampaign() {
	if (isGenerating.value || isCreatingKlaviyoCampaign.value || !taskId.value) return;

	isCreatingKlaviyoCampaign.value = true;
	klaviyoStatusMessage.value = 'Resyncing...';

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/resync-klaviyo-campaign`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error('Failed to resync Klaviyo campaign');
		}

		await checkKlaviyoCampaignStatus(); // Check status immediately
		startKlaviyoStatusPolling(); // Start polling

		customerIOTrackEvent('klaviyo_campaign_resync_started', {
			taskId: taskId.value,
			campaignName: task.value?.campaign?.name
		});
	} catch (error) {
		console.error('Error resyncing Klaviyo campaign:', error);
		isCreatingKlaviyoCampaign.value = false;
		klaviyoStatusMessage.value = 'Failed';
		if (taskId.value) await fetchTask(taskId.value); // Refresh task on error
	}
}

async function resyncKlaviyoCampaignHtmlOnly() {
	if (isGenerating.value || isCreatingKlaviyoCampaign.value || !taskId.value) return;

	isCreatingKlaviyoCampaign.value = true;
	klaviyoStatusMessage.value = 'Resyncing...';

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/resync-klaviyo-campaign-html-only`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error('Failed to resync Klaviyo campaign (HTML-only)');
		}

		await checkKlaviyoCampaignStatus(); // Check status immediately
		startKlaviyoStatusPolling(); // Start polling

		customerIOTrackEvent('klaviyo_campaign_html_only_resync_started', {
			taskId: taskId.value,
			campaignName: task.value?.campaign?.name
		});
	} catch (error) {
		console.error('Error resyncing Klaviyo campaign (HTML-only):', error);
		isCreatingKlaviyoCampaign.value = false;
		klaviyoStatusMessage.value = 'Failed';
		if (taskId.value) await fetchTask(taskId.value); // Refresh task on error
	}
}

async function createKlaviyoCampaignHtmlOnly() {
	if (isGenerating.value || isCreatingKlaviyoCampaign.value || !taskId.value) return;

	isCreatingKlaviyoCampaign.value = true;
	klaviyoStatusMessage.value = 'Creating...';

	try {
		const htmlContent = task.value?.emailHTML || '';
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/create-klaviyo-campaign-html-only`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ html: htmlContent })
		});

		if (!response.ok) {
			throw new Error('Failed to create Klaviyo campaign (HTML-only)');
		}

		await checkKlaviyoCampaignStatus(); // Check status immediately
		startKlaviyoStatusPolling(); // Start polling

		customerIOTrackEvent('klaviyo_campaign_html_only_creation_started', {
			taskId: taskId.value,
			campaignName: task.value?.campaign?.name
		});
	} catch (error) {
		console.error('Error creating Klaviyo campaign (HTML-only):', error);
		isCreatingKlaviyoCampaign.value = false;
		klaviyoStatusMessage.value = 'Failed';
		if (taskId.value) await fetchTask(taskId.value); // Refresh task on error
	}
}

async function checkKlaviyoCampaignStatus() {
	if (!taskId.value) return;

	try {
		const statusResponse = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/klaviyo-status`, {
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			}
		});

		if (statusResponse.ok) {
			const status = await statusResponse.json();
			if (status.status === 'pending' || status.status === 'processing') {
				isCreatingKlaviyoCampaign.value = true;
				klaviyoStatusMessage.value = status.step || 'Creating...';
				klaviyoStep.value = status.step;
				startKlaviyoStatusPolling(); // Ensure polling starts if detected in progress
			} else if (status.status === 'completed') {
                isCreatingKlaviyoCampaign.value = false; // Ensure flag is reset
				if (taskId.value) await fetchTask(taskId.value); // Refresh task
			} else {
                 isCreatingKlaviyoCampaign.value = false; // Reset flag for other statuses (e.g., failed, or initial null)
            }
		} else {
             isCreatingKlaviyoCampaign.value = false; // Reset flag on non-ok response
        }
	} catch (error) {
		console.error('Error checking Klaviyo campaign status:', error);
        isCreatingKlaviyoCampaign.value = false; // Reset flag on error
	}
}

function startKlaviyoStatusPolling() {
	if (klaviyoPollingInterval.value) clearInterval(klaviyoPollingInterval.value);

	klaviyoPollingInterval.value = setInterval(async () => {
        if (!taskId.value) {
            clearInterval(klaviyoPollingInterval.value!);
            return;
        }
		try {
			const statusResponse = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/klaviyo-status`, {
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				}
			});

			if (statusResponse.ok) {
				const status = await statusResponse.json();
				if (status.status === 'processing') {
					klaviyoStatusMessage.value = status.step || 'Processing...';
					klaviyoStep.value = status.step;
				} else if (status.status === 'completed') {
					clearInterval(klaviyoPollingInterval.value!);
					isCreatingKlaviyoCampaign.value = false;
					if (taskId.value) await fetchTask(taskId.value); // Refresh task
					customerIOTrackEvent('klaviyo_campaign_created', {
						taskId: taskId.value,
						campaignName: task.value?.campaign?.name
					});
				} else if (status.status === 'failed') {
					clearInterval(klaviyoPollingInterval.value!);
					isCreatingKlaviyoCampaign.value = false;
					klaviyoStatusMessage.value = 'Failed: ' + (status.error || 'Unknown error');
					// Show error toast
					showToast('Failed to sync campaign with Klaviyo: ' + (status.error || 'Unknown error'), 'error');
					customerIOTrackEvent('klaviyo_campaign_failed', {
						taskId: taskId.value,
						campaignName: task.value?.campaign?.name,
						error: status.error
					});
				}
			} else {
                 console.error('Klaviyo status polling failed:', statusResponse.status);
                 clearInterval(klaviyoPollingInterval.value!);
                 isCreatingKlaviyoCampaign.value = false; // Stop indicator on poll failure
            }
		} catch (error) {
			console.error('Error polling Klaviyo status:', error);
            clearInterval(klaviyoPollingInterval.value!); // Stop polling on error
            isCreatingKlaviyoCampaign.value = false;
		}
	}, 2000);
}

function openRegenerateModal() {
	showRegenerateModal.value = true;
}

function handleRegenerateSubmit(userPrompt: string) {
	showRegenerateModal.value = false;

	const contentStep = taskSteps.value.find(step => step.taskTypeId === TASK_TYPES.CONTENT);
	let currentBriefText = 'Not set';
	if (contentStep?.data) {
		try {
			// Check if the data contains <brief> tags and extract the content
			let dataToProcess = String(contentStep.data);
			const briefTagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
			const match = dataToProcess.match(briefTagRegex);

			if (match && match[1]) {
				// Found content between <brief> tags, use that for parsing
				dataToProcess = match[1].trim();
			}

			// Use safeParseJson instead of JSON.parse
			const parsedData = safeParseJson(dataToProcess) as { briefText?: string };
			currentBriefText = parsedData?.briefText || 'Brief text property missing or empty';
		} catch (error) {
			console.error('Failed to parse content step data for regeneration:', error, contentStep.data);
			if (typeof contentStep.data === 'string') {
				currentBriefText = contentStep.data; // Fallback
			} else {
                currentBriefText = 'Error parsing brief data';
            }
		}
	}

	const contextHeader = "\n\nUSE THE FOLLOWING BRIEF TEXT IF SUPPLIED, MAKE CHANGES BASED ON THE USERS ASK. ONLY CHANGE THE THINGS THE USER ASKS FOR. The previous Brief details are:";
	const contextSubject = `Subject Line: ${subjectLineRef.value || 'Not set'}`; // Use ref
	const contextPreview = `Preview Line: ${previewLineRef.value || 'Not set'}`; // Use ref
	const contextBrief = `Brief Text:\n${currentBriefText}`;
	const enhancedPrompt = `${userPrompt}${contextHeader}\n${contextSubject}\n${contextPreview}\n${contextBrief}`;

	regenerateContent(enhancedPrompt);
}

async function regenerateContent(userPrompt: string) {
	if (!taskId.value) return;

	isGenerating.value = true;
	startMessageAnimation();

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/regenerate`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ userPrompt: userPrompt || undefined }),
		});

		if (response.ok) {
			// const result = await response.json(); // Process result if needed
			await fetchTask(taskId.value); // Refresh task to get regenerated content
		} else {
			console.error('Failed to regenerate content');
            // Optionally show error to user
		}
	} catch (error) {
		console.error('Error regenerating content:', error);
	} finally {
		isGenerating.value = false;
		if (messageInterval.value) clearInterval(messageInterval.value);
	}
}

async function regenerateEmail() {
	if (isGeneratingEmail.value) return;
	await generateNewEmail(); // Reuse existing method
}

function showEmailPreview() {
    // Accessing methods on child component requires template ref
    // Ensure contentTaskRef is correctly assigned in the template
	if (contentTaskRef.value) {
		//contentTaskRef.value.showPreview();
	} else {
        console.warn('ContentTask ref not available');
    }
}

async function saveDesign(design: any) {
    if (!taskId.value) return;
	try {
		// If design contains HTML, save HTML separately first (if applicable)
		if (design?.html) {
			const html = design.html;
			const htmlResponse = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/save-email-html`, {
				method: 'POST',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ html })
			});
			if (!htmlResponse.ok) throw new Error('Failed to save email HTML');
		}

		// Save the JSON design object to the main task
        // Handle case where design is null (clearing the design)
		if (design === null) {
			const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}`, {
				method: 'PATCH',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ emailDesign: null })
			});

			if (!response.ok) {
				throw new Error('Failed to clear email design');
			}

			// Update local state
			emailDesign.value = null;
			await fetchTask(taskId.value);
			return;
		}

		// Handle case where design object exists
		if (design?.design) {
			// Ensure we are sending a clean JSON object, not Vue Proxy
			const cleanDesign = JSON.parse(JSON.stringify(design.design));
			const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}`, {
				method: 'PATCH',
				headers: {
					'Authorization': `Bearer ${localStorage.getItem('token')}`,
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ emailDesign: cleanDesign })
			});

			if (!response.ok) {
				throw new Error('Failed to save email design');
			}

			// Update local state and potentially refresh task
			emailDesign.value = cleanDesign; // Update local ref
			await fetchTask(taskId.value); // Optional: Refresh full task data if needed
		}
	} catch (error) {
		console.error('Error saving email design:', error);
	}
}

async function exportHtml() {
	if (!emailEditor.value) return;
	try {
		const html = await emailEditor.value.exportHtml(); // Assuming exportHtml is async
		const blob = new Blob([html], { type: 'text/html' });
		const url = window.URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `email-${task.value?.campaign?.name || 'template'}.html`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		window.URL.revokeObjectURL(url);
	} catch (error) {
		console.error('Error exporting HTML:', error);
	}
}

function onEditorLoaded(editorInstance: any) {
	emailEditor.value = editorInstance;
	loadEmailDesign(emailDesign.value || task.value?.emailDesign); // Load initial design
}

function loadEmailDesign(design: any) {
	if (!design || !emailEditor.value) return;
    // Ensure we load a clean object, not a Vue Proxy
	emailEditor.value.loadDesign(JSON.parse(JSON.stringify(design)));
}

function loadPendingDesign() {
    // This might be called when the modal is ready after generation
	loadEmailDesign(emailDesign.value || task.value?.emailDesign);
}

async function generateNewEmail() {
	if (isGeneratingEmail.value || !taskId.value) return;

	// Clear existing email design locally and trigger save to backend
    emailDesign.value = null;
    if (task.value) task.value.emailDesign = null; // Clear in task object too
    await saveDesign(null); // Save null design to backend

	isGeneratingEmail.value = true;
	showEmailModal.value = false; // Don't show modal immediately
	currentMessageIndex.value = 0;
	currentGenerationMessages.value = [...generationMessages];
	startMessageAnimation();
	startCountdown();

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/email-content`, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error('Failed to start email generation');
		}

		startEmailStatusPolling(); // Start polling for completion

	} catch (error) {
		console.error('Error generating email:', error);
		isGeneratingEmail.value = false;
		if (pollingInterval.value) clearInterval(pollingInterval.value);
		if (messageInterval.value) clearInterval(messageInterval.value);
		if (countdownInterval.value) clearInterval(countdownInterval.value);
	}
}

function startCountdown() {
	if (countdownInterval.value) clearInterval(countdownInterval.value);
	countdownTime.value = 120;
	showAnySecond.value = false;
	countdownInterval.value = setInterval(() => {
		if (countdownTime.value > 0) {
			countdownTime.value--;
		} else {
			showAnySecond.value = true;
            // Optionally stop interval when it hits zero and stays at "any second"
            // clearInterval(countdownInterval.value!);
		}
	}, 1000);
}

async function handleContentUpdate(payload: { id: string, newData: string }) {
	const { id, newData } = payload;

	const contentStepIndex = taskSteps.value.findIndex(step => step.id === id);
	if (contentStepIndex !== -1) {
		// Update local data immediately
		taskSteps.value[contentStepIndex].data = newData;
		// Also update selectedTask if it's the one being edited
		if (selectedTask.value && selectedTask.value.id === id) {
			selectedTask.value.data = newData;
            // Directly update refs to ensure UI updates immediately
            try {
                const parsed = JSON.parse(newData);
                subjectLineRef.value = parsed.subjectLine || '';
                previewLineRef.value = parsed.previewText || '';
            } catch(e) { console.error("Error parsing during content update", e); }
		}
		await saveContent(id, newData); // Save to backend
	} else {
		console.error(`Could not find the content task step with ID ${id} to update.`);
	}
}

async function saveContent(stepId: string, newData: string) {
	if (!taskId.value || !stepId) {
		console.error('Cannot save content: Missing taskId or stepId.');
		return;
	}
	try {
		const response = await fetch(`${URL_DOMAIN}/planner/task/${taskId.value}/taskstep/${stepId}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ data: newData })
		});

		if (!response.ok) {
			const errorBody = await response.text();
			console.error(`Failed to save content for step ${stepId}. Status:`, response.status, 'Body:', errorBody);
			throw new Error(`Failed to save content: ${response.status}`);
		}
	} catch (error) {
		console.error('Error saving content:', error);
	}
}

function startMessageAnimation() {
	if (messageInterval.value) clearInterval(messageInterval.value);
	let index = 0;
    currentMessageIndex.value = 0; // Reset index
	messageInterval.value = setInterval(() => {
		if (index < generationMessages.length) {
			currentMessageIndex.value = index;
			index++;
		} else {
            // Optional: Stop interval when messages run out
            // clearInterval(messageInterval.value!);
            index = 0; // Loop animation
            currentMessageIndex.value = index;
		}
	}, 3000);
}

async function updateCampaignName(newName: string) {
	if (!task.value?.campaign?.id) {
		console.error('Cannot update campaign name: Missing campaign ID');
		return;
	}

	const campaignId = task.value.campaign.id;

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ name: newName })
		});

		if (!response.ok) {
			const errorBody = await response.text();
			console.error(`Failed to update campaign name. Status:`, response.status, 'Body:', errorBody);
			throw new Error(`Failed to update campaign name: ${response.status}`);
		}

		const result = await response.json();

		// Update the local task object with the new campaign name
		if (task.value && task.value.campaign) {
			task.value.campaign.name = newName;

			// Show a temporary success message
			const topBar = document.querySelector('.bg-white.border-b');
			if (topBar) {
				const successMessage = document.createElement('div');
				successMessage.className = 'absolute top-2 right-2 bg-green-100 text-green-800 px-4 py-2 rounded-md text-sm transition-opacity duration-500';
				successMessage.textContent = 'Campaign name updated successfully';
				topBar.appendChild(successMessage);

				// Fade out and remove after 3 seconds
				setTimeout(() => {
					successMessage.style.opacity = '0';
					setTimeout(() => {
						successMessage.remove();
					}, 500);
				}, 2500);
			}
		}
	} catch (error) {
		console.error('Error updating campaign name:', error);
	}
}

async function updateCampaignDescription(newDescription: string) {
	if (!task.value?.campaign?.id) {
		console.error('Cannot update campaign description: Missing campaign ID');
		return;
	}

	const campaignId = task.value.campaign.id;

	try {
		const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ description: newDescription })
		});

		if (!response.ok) {
			const errorBody = await response.text();
			console.error(`Failed to update campaign description. Status:`, response.status, 'Body:', errorBody);
			throw new Error(`Failed to update campaign description: ${response.status}`);
		}

		const result = await response.json();

		// Update the local task object with the new campaign description
		if (task.value && task.value.campaign) {
			task.value.campaign.description = newDescription;

			// Show a temporary success message
			const topBar = document.querySelector('.bg-white.border-b');
			if (topBar) {
				const successMessage = document.createElement('div');
				successMessage.className = 'absolute top-2 right-2 bg-green-100 text-green-800 px-4 py-2 rounded-md text-sm transition-opacity duration-500';
				successMessage.textContent = 'Campaign description updated successfully';
				topBar.appendChild(successMessage);

				// Fade out and remove after 3 seconds
				setTimeout(() => {
					successMessage.style.opacity = '0';
					setTimeout(() => {
						successMessage.remove();
					}, 500);
				}, 2500);
			}
		}
	} catch (error) {
		console.error('Error updating campaign description:', error);
	}
}

async function updateSegment(segmentData: any) {
	const campaignId = task.value?.campaign?.id;
	if (!campaignId) {
		console.error('No campaign ID available to update the segment');
		return;
	}

	try {
		// Handle both Raleon and Klaviyo segments
		let updatePayload: any = { targetSegment: segmentData.name };

		// If it's a Klaviyo segment, include additional data for temporary storage
		if (segmentData.segmentType === 'klaviyo' && segmentData.klaviyoSegmentId) {
			updatePayload.klaviyoSegmentId = segmentData.klaviyoSegmentId;
			updatePayload.segmentType = 'klaviyo';
		}

		const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
			method: 'PATCH',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(updatePayload)
		});

		if (!response.ok) {
			console.error('Update segment API failed:', response.status, response.statusText);
			throw new Error('Failed to update target segment');
		}

		// Update local task data to reflect the segment change
		if (task.value) {
			task.value.segment = segmentData;
		}

		// const responseData = await response.json(); // Process response if needed
		if (taskId.value) await fetchTask(taskId.value); // Refresh task
	} catch (error) {
		console.error('Error updating segment:', error);
	}
}

function handleBackNavigation() {
	const planId = route.query.planId;
	const fromPlanning = route.query.fromPlanning;

	if (fromPlanning === 'true') {
		router.push('/ai-strategist/planning');
	} else if (planId) {
		router.push(`/ai-strategist/planning/plan/${planId}`);
	} else {
		router.push('/ai-strategist/tasks');
	}
}

async function handleTaskUpdated(updatedTaskData: any) {
    // This seems to be for ScheduleTask updates
    const campaignId = updatedTaskData?.campaign?.id;
    const scheduledDate = updatedTaskData?.campaign?.scheduledDate;

	if (task.value && campaignId && scheduledDate) {
        // Update local task object immediately for reactivity
        task.value = {
            ...task.value,
            campaign: {
                ...task.value.campaign,
                scheduledDate: scheduledDate
            }
        };

        // Save change to the server (optional, could be handled by ScheduleTask itself)
        try {
            const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ scheduledDate: scheduledDate }),
            });

            if (!response.ok) {
                console.error('API Error updating schedule:', response.status);
                throw new Error('Failed to update task schedule on server');
            }
            // const result = await response.json();
            // Optional: Refresh task if server response contains more updated data
            // if (taskId.value) await fetchTask(taskId.value);
        } catch (error) {
            console.error('Error updating task schedule on server:', error);
            // Optionally revert local change or show error
        }
    } else {
         console.warn('Could not handle task update, missing data:', {campaignId, scheduledDate});
    }
}

async function handleEmailJobTaskUpdated() {
    // Handle task updated event from EmailJobsView (when variation is selected)
    if (taskId.value) {
        console.log('Email job task updated, refreshing task data');
        await fetchTask(taskId.value);
        
        // Switch to CONTENT task tab if available
        const contentTask = taskSteps.value.find(step => step.taskTypeId === TASK_TYPES.CONTENT);
        if (contentTask) {
            selectedTask.value = contentTask;
        }
    }
}

function handleScheduleError(error: any) {
	console.error('Error scheduling task:', error);
    // Show error message to user
}

// Handle the use-in-email event from ImageGenerationTask
function handleUseImageInEmail(imageData: { url: string, name: string, jsonData?: string }) {

	// Find the content task
	const contentTask = taskSteps.value.find(step => step.taskTypeId === TASK_TYPES.CONTENT);
	if (!contentTask) {
		console.error('Content task not found');
		return;
	}

	// Store the image data for later use with the new JSON-based image tag format
	// If jsonData is provided, use it; otherwise, fall back to the old format
	const message = imageData.jsonData
		? `Use this image <imageref>${imageData.jsonData}</imageref> in my email.`
		: `Use this image ${imageData.name} <imageref>${imageData.url}</imageref> in my email.`;

	// Switch to the content task
	selectedTask.value = contentTask;

	// Force reload the conversation if we have a conversation ID
	// This ensures the chat history is loaded when switching tabs
	if (task?.value?.conversationId) {

		// Set a small delay to ensure the ContentTask component is mounted
		setTimeout(() => {
			if (contentTaskRef.value) {
				// Force reload by setting the forceReloadConversation flag
				contentTaskRef.value.forceReloadConversation = true;

				// Reset the flag after a short delay
				setTimeout(() => {
					if (contentTaskRef.value) {
						contentTaskRef.value.forceReloadConversation = false;
					}
				}, 100);
			}
		}, 200);
	}

	// Use a longer delay and multiple attempts to ensure the component is fully mounted
	let attempts = 0;
	const maxAttempts = 5;
	const attemptInterval = 300; // 300ms between attempts

	const trySendMessage = () => {
		attempts++;

		if (contentTaskRef.value && contentTaskRef.value.briefChatRef) {
			try {
				// Auto-send the message directly using the sendExternalMessage method
				contentTaskRef.value.briefChatRef.sendExternalMessage(message);
			} catch (error) {
				console.error('Error sending message in BriefChat:', error);
				if (attempts < maxAttempts) {
					setTimeout(trySendMessage, attemptInterval);
				}
			}
		} else {
			if (attempts < maxAttempts) {
				setTimeout(trySendMessage, attemptInterval);
			} else {
				console.error('Failed to access BriefChat component after multiple attempts');
			}
		}
	};

	// Start the first attempt after a delay to allow for component mounting
	// Use a longer delay to ensure the conversation has time to reload
	setTimeout(trySendMessage, 800);
}

// --- Update Subject/Preview Handlers (using Refs) ---
function updateSubjectLine(newSubject: string) {
	if (!selectedTask.value) {
		console.error('Cannot update subject line: No selected task.');
		return;
	}
	try {
        // Update the ref immediately
        subjectLineRef.value = newSubject;

        // Update the underlying data string
		const currentData = selectedTaskData.value ? { ...selectedTaskData.value } : {};
		const updatedData = { ...currentData, subjectLine: newSubject };
		const newJsonString = JSON.stringify(updatedData, null, 2);

		// Update local data string in selectedTask and taskSteps
		selectedTask.value.data = newJsonString;
		const stepIndex = taskSteps.value.findIndex(step => step.id === selectedTask.value!.id);
		if (stepIndex !== -1) {
			taskSteps.value[stepIndex].data = newJsonString;
		}

		// Trigger save
		saveContent(selectedTask.value.id, newJsonString);

	} catch (error) {
		console.error('Error processing subject line update:', error);
	}
}

function updatePreviewLine(newPreview: string) {
	if (!selectedTask.value) {
		 console.error('Cannot update preview line: No selected task.');
		 return;
	}
	try {
        // Update the ref immediately
        previewLineRef.value = newPreview;

        // Update the underlying data string
		const currentData = selectedTaskData.value ? { ...selectedTaskData.value } : {};
		const updatedData = { ...currentData, previewText: newPreview }; // Assuming key is previewText
		const newJsonString = JSON.stringify(updatedData, null, 2);

		// Update local data string
		selectedTask.value.data = newJsonString;
		const stepIndex = taskSteps.value.findIndex(step => step.id === selectedTask.value!.id);
		if (stepIndex !== -1) {
			taskSteps.value[stepIndex].data = newJsonString;
		}

		// Trigger save
		saveContent(selectedTask.value.id, newJsonString);

	} catch (error) {
		console.error('Error processing preview line update:', error);
	}
}


// --- Lifecycle Hooks ---
onMounted(async () => {
	// taskId is set by the watcher now
	if (taskId.value) {
		// await fetchTask(taskId.value); // Watcher handles initial fetch
		await checkEmailGenerationStatus();
		await checkKlaviyoCampaignStatus();
	} else {
        // Handle case where taskId isn't available on mount (e.g., direct navigation issue)
        console.warn('No taskId available on mount.');
        isInitializing.value = false; // Allow UI to render potentially empty state
    }
});

// --- Toast Notification ---
const toast = ref({
	show: false,
	message: '',
	type: 'info',
	timeout: null as NodeJS.Timeout | null
});

function showToast(message: string, type = 'info', duration = 5000) {
	// Clear any existing toast timeout
	if (toast.value.timeout) {
		clearTimeout(toast.value.timeout);
	}

	// Set toast properties
	toast.value.message = message;
	toast.value.type = type;
	toast.value.show = true;

	// Auto-hide toast after duration
	if (duration > 0) {
		toast.value.timeout = setTimeout(() => {
			closeToast();
		}, duration);
	}
}

function closeToast() {
	toast.value.show = false;
	if (toast.value.timeout) {
		clearTimeout(toast.value.timeout);
		toast.value.timeout = null;
	}
}

// --- Cleanup ---
// Use onUnmounted in Composition API if needed for cleanup, e.g., clearing intervals
// import { onUnmounted } from 'vue';
// onUnmounted(() => {
//   if (countdownInterval.value) clearInterval(countdownInterval.value);
//   if (pollingInterval.value) clearInterval(pollingInterval.value);
//   if (klaviyoPollingInterval.value) clearInterval(klaviyoPollingInterval.value);
//   if (messageInterval.value) clearInterval(messageInterval.value);
// });

</script>

<style>
/* Styles remain the same */
@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}
.animate-shimmer {
	animation: shimmer 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	background-size: 200% 100%;
}
.animate-spin {
	animation: spin 1s linear infinite;
}
@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}
.fade-enter-active,
.fade-leave-active {
	transition: all 0.5s ease;
}
.fade-enter-from,
.fade-leave-to {
	opacity: 0;
	transform: translateY(20px);
}
.fade-enter-to,
.fade-leave-from {
	opacity: 1;
	transform: translateY(0);
}
</style>
